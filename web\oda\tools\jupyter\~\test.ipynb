{"cells": [{"cell_type": "text", "source": "# Level 1", "metadata": {"id": "808491dbe2"}}, {"cell_type": "text", "source": "Level 1", "metadata": {"id": ""}}, {"cell_type": "text", "source": "## Level 2", "metadata": {"id": "3f9875d631"}}, {"cell_type": "text", "source": "Level 2", "metadata": {"id": ""}}, {"collapsed": true, "cell_type": "text", "source": "### The Philosophy of Bayesian Inference ...", "metadata": {"id": "16a7c011d96", "collapsed": true}}, {"cell_type": "text", "source": "**<h4>The Philosophy of Bayesian Inference</h4>**\n  \n> You are a skilled programmer, but bugs still slip into your code. After a particularly difficult implementation of an algorithm, you decide to test your code on a trivial example. It passes. You test the code on a harder problem. It passes once again. And it passes the next, *even more difficult*, test too! You are starting to believe that there may be no bugs in this code...\n\nIf you think this way, then congratulations, you already are thinking Bayesian! Bayesian inference is simply updating your beliefs after considering new evidence. A Bayesian can rarely be certain about a result, but he or she can be very confident. Just like in the example above, we can never be 100% sure that our code is bug-free unless we test it on every possible problem; something rarely possible in practice. Instead, we can test it on a large number of problems, and if it succeeds we can feel more *confident* about our code, but still not certain.  Bayesian inference works identically: we update our beliefs about an outcome; rarely can we be absolutely sure unless we rule out all other alternatives. ", "metadata": {"id": "16a7c011d96"}}, {"collapsed": true, "cell_type": "text", "source": "### Introducing our first hammer: PyMC3 ...", "metadata": {"id": "7acd2d54b7", "collapsed": true}}, {"cell_type": "text", "source": "**<h4>Introducing our first hammer: PyMC3</h4>**\n\n\nPyMC3 is a Python library for programming Bayesian analysis [3]. It is a fast, well-maintained library. The only unfortunate part is that its documentation is lacking in certain areas, especially those that bridge the gap between beginner and hacker. One of this book's main goals is to solve that problem, and also to demonstrate why PyMC3 is so cool.\n\nWe will model the problem above using PyMC3. This type of programming is called *probabilistic programming*, an unfortunate misnomer that invokes ideas of randomly-generated code and has likely confused and frightened users away from this field. The code is not random; it is probabilistic in the sense that we create probability models using programming variables as the model's components. Model components are first-class primitives within the PyMC3 framework. \n\nB. Cronin [5] has a very motivating description of probabilistic programming:\n\n>   Another way of thinking about this: unlike a traditional program, which only runs in the forward directions, a probabilistic program is run in both the forward and backward direction. It runs forward to compute the consequences of the assumptions it contains about the world (i.e., the model space it represents), but it also runs backward from the data to constrain the possible explanations. In practice, many probabilistic programming systems will cleverly interleave these forward and backward operations to efficiently home in on the best explanations.\n\nBecause of the confusion engendered by the term *probabilistic programming*, I'll refrain from using it. Instead, I'll simply say *programming*, since that's what it really is. \n\nPyMC3 code is easy to read. The only novel thing should be the syntax. Simply remember that we are representing the model's components ($\\tau, \\lambda_1, \\lambda_2$ ) as variables.", "metadata": {"id": "bf2349250"}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": "#@title Copyright 2020 Google LLC. Double-click here for license information.\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this file except in compliance with the License.\n# You may obtain a copy of the License at\n#\n# https://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.", "metadata": {"cellView": "form", "id": "wDlWLbfkJtvu", "mode": "html", "showGutter": false, "hideRun": true, "hideCode": false, "hideResult": true, "hideConsole": true, "border": "none", "collapsed": true}, "collapsed": true}, {"cell_type": "text", "source": "**Lorem ipsum**\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.", "metadata": {"id": "17dd76cc8ca"}}, {"collapsed": true, "cell_type": "text", "source": "### Markdown demo ...", "metadata": {"id": "e91e2e4080", "collapsed": true}}, {"cell_type": "text", "source": "| Syntax | Description |\n| ----------- | ----------- |\n| Header | Title |\n| Paragraph | Text |\n\n", "metadata": {"id": "16c127ca60b"}}, {"cell_type": "text", "source": "You can align text in the columns to the left, right, or center by adding a colon (:) to the left, right, or on both side of the hyphens within the header row.\n\n| Syntax      | Description | Test Text     |\n| :---        |    :----:   |          ---: |\n| Header      | Title       | Here's this   |\n| Paragraph   | Text        | And more      |", "metadata": {"id": "bc4a765f32"}}, {"cell_type": "text", "source": "```\n{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"age\": 25\n}\n```", "metadata": {"id": "121ac2f38a6"}}, {"cell_type": "text", "source": "[Markdown Guide](https://www.markdownguide.org)\n\n<del>The world is flat.</del>\n\n**bold text**\n\n- <input type=\"checkbox\"> Write the press release\n- <input type=\"checkbox\" checked> Update the website\n- <input type=\"checkbox\" checked> Contact the media\n\nH<sub>2</sub>O\n\nX<sup>2</sup>\n\n***\n\n---\n\n___\n\n- First item\n- Second item\n- Third item\n\n1. First item\n2. Second item\n3. Third item\n    - Indented item\n    - Indented item\n4. Fourth item\n\nBlockquotes can be nested. Add a >> in front of the paragraph you want to nest.\n\n> <PERSON> followed her through many of the beautiful rooms in her castle.\n>\n>> The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.\n\n* This is the first list item.\n* Here's the second list item.\n\n    > A blockquote would look great below the second list item.\n\n* And here's the third list item.\n\nTo create a line break or new line \\<br>, end a line with two or more spaces, and then type return.   \nThis is the first line.  \nAnd this is the second line.\n\n\n\n", "metadata": {"id": "17b05e2a5e"}}, {"collapsed": true, "cell_type": "text", "source": "### MathJax demo...", "metadata": {"id": "c048cf1237", "collapsed": true}}, {"cell_type": "text", "source": "<p style=\"margin: 32px 0\"></p>\n$x=\\frac{ -b\\pm\\sqrt{ b^2-4ac } } {2a}$\n\n$$x=\\frac{ -b\\pm\\sqrt{ b^2-4ac } } {2a}$$\n\n#$E=mc^2$", "metadata": {"id": "488cd261ff"}}, {"collapsed": true, "cell_type": "text", "source": "### Markdown H1-H6 ...", "metadata": {"id": "16b3d958936", "collapsed": true}}, {"cell_type": "text", "source": "H1\n---\n\n## H2\n### H3\n#### H4\n##### H5\n###### H6", "metadata": {"id": "141f8165720"}}]}