export class Activation{
    static sigmoid(discrete_steps = 0, max = 6){
        if (!discrete_steps){
            return function (tensor, target){
                let out = torus.get_out(tensor, 'sigmoid');
                if (!out){
                    out = torus.from(new Float64Array(tensor.size))._shape(tensor)._src(tensor)._label('sigmoid: ' + tensor.shape);
                    torus.set_out(tensor, out, 'sigmoid');
                    out._fwd = ()=>{
                        for (let i = 0; i<tensor.size; i++){
                            let x = tensor.data[i];
                            out.data[i] = 1 / (1 + Math.exp(-x));
                        }
                        return out;
                    }
                    out._back = ()=>{
                        for (let i = 0; i<tensor.size; i++){
                            let y = out.data[i];
                            tensor.grad.data[i] += out.grad.data[i] * y * (1 - y);
                        }
                    }
                }
                return out._fwd();
            }
        }
        return function (tensor, target){
            let out = torus.get_out(tensor, 'sigmoid');
            if (!out){
                out = torus.from(new Float64Array(tensor.size))._shape(tensor)._src(tensor)._label('sigmoid: ' + tensor.shape);
                torus.set_out(tensor, out, 'sigmoid');
                out._fwd = ()=>{
                    for (let i = 0; i<tensor.size; i++){
                        let x = tensor.data[i];
                        out.data[i] = 1 / (1 + Math.exp(-x));
                    }
                    return out;
                }
                out._back = ()=>{
                    for (let i = 0; i<tensor.size; i++){
                        let y = out.data[i];
                        tensor.grad.data[i] += out.grad.data[i] * y * (1 - y);
                    }
                }
            }
            return out._fwd();
        }

    }
}