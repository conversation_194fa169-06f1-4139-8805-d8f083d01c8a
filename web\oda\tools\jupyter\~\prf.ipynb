{"cells": [{"cell_type": "code", "source": ["// 0001\nITERATION_TOTAL = 1000000\n\niterations = ITERATION_TOTAL\n_myArray = Array.from(Array(iterations).keys())\n\ntotal = 0"], "metadata": {"id": "0001", "hideOutput": false}, "time": "40 ms", "state": "", "outputs": []}, {"cell_type": "code", "source": ["// 0002\nconst myArray = _myArray\nlet total = 0\n\nconst start = performance.now()\n\nfor (let i = 0; i < myArray.length; i++) {\n  total += myArray[i]\n}\n\nconst end = performance.now()\n\n>tm_for = `for - ${Math.round(end - start)} ms.`;\n"], "metadata": {"id": "0002", "hideOutput": false}, "time": "4 ms", "state": "", "outputs": [{"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tm_for = `for - ${Math.round(end - start)} ms.`</label>"}}, {"data": {"text/plain": "for - 4 ms."}}]}, {"cell_type": "code", "source": ["// 0003\nconst myArray = _myArray\nlet total = 0\n\nconst start = performance.now()\n\nlet i = 0\n\nwhile (i < myArray.length) {\n  total += myArray[i]\n  i++\n}\n\nconst end = performance.now()\n\n>tm_while = `while - ${Math.round(end - start)} ms.`;\n"], "metadata": {"id": "0003", "hideOutput": false}, "time": "3 ms", "state": "", "outputs": [{"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tm_while = `while - ${Math.round(end - start)} ms.`</label>"}}, {"data": {"text/plain": "while - 3 ms."}}]}, {"cell_type": "code", "source": ["// 0004\nconst myArray = _myArray\nlet total = 0\n\nconst start = performance.now()\nfor (const item of myArray) {\n  total += item\n}\n\nconst end = performance.now()\n\n>tm_forOf = `for of - ${Math.round(end - start)} ms.`;\n"], "metadata": {"id": "0004", "hideOutput": false}, "time": "19 ms", "state": "", "outputs": [{"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tm_forOf = `for of - ${Math.round(end - start)} ms.`</label>"}}, {"data": {"text/plain": "for of - 18 ms."}}]}, {"cell_type": "code", "source": ["// 0005\nconst myArray = _myArray\nlet total = 0\n\nconst start = performance.now()\n\nmyArray.forEach((item) => {\n  total += item\n})\n\nconst end = performance.now()\n\n>tm_forEach = `forEach - ${Math.round(end - start)} ms.`;\n"], "metadata": {"id": "0005", "hideOutput": false}, "time": "11 ms", "state": "", "outputs": [{"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tm_forEach = `forEach - ${Math.round(end - start)} ms.`</label>"}}, {"data": {"text/plain": "forEach - 11 ms."}}]}, {"cell_type": "code", "source": ["// 0006\nconst myArray = _myArray\nlet total = 0\n\nconst start = performance.now()\n\nmyArray.map((item) => {\n  total += item\n})\n\nconst end = performance.now()\n\n>tm_map = `map - ${Math.round(end - start)} ms.`;\n\n\n\n\n\n\n\n\n// Long string: --test-color: light-dark(oklch(from var(--main-color) l 2 h), oklch(from var(--main-color) 1 .01 h)); --test-color: light-dark(oklch(from var(--main-color) l 2 h), oklch(from var(--main-color) 1 .01 h)); --test-color: light-dark(oklch(from var(--main-color) l 2 h), oklch(from var(--main-color) 1 .01 h));\n\n\n\n\n\n\n\n"], "metadata": {"id": "0006"}, "time": "16 ms", "outputs": [{"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tm_map = `map - ${Math.round(end - start)} ms.`</label>"}}, {"data": {"text/plain": "map - 16 ms."}}], "state": ""}, {"cell_type": "code", "source": ["ODA({ is:'oda-color', template: `\n        <style>\n            :host { @apply --vertical }\n            button { padding: 4px;  margin: 1px; font-size: large; width: {{_w}}px; height: {{_h}}px; cursor: pointer; }\n            .test {\n                --test-background: light-dark(oklch(from var(--main-color) 1 .01 h), oklch(from var(--main-color) l 2 h));\n                --test-color: light-dark(oklch(from var(--main-color) l 2 h), oklch(from var(--main-color) 1 .01 h));\n                background-color: var(--test-background);\n                color: var(--test-color);\n                fill: var(--test-color);\n            }\n            h3 { color: red; margin: 0; padding: 0; margin-left: 16px; color: {{color}}; }\n        </style>\n        <h3>{{count}}</h3>\n        <div vertical>\n            <button ~for=\"vars\" ~class=\"$for.item\">{{$for.item}}</button>\n        </div>\n    `,\n    vars: ['test', 'header', 'dark', 'light'], count: 0, _h: 40, _w: 120, color: 'red'\n})\n\n>el = this.create('oda-color')\n"], "time": "", "outputs": []}, {"cell_type": "code", "source": [">el"], "metadata": {"id": "eac1a171ad"}, "time": ""}]}