import * as Auth from '/web/auth/auth.js';
export default ODANT({
    is: 'odant-unavailable-dialog', imports: '@oda/title',
    template: /*html*/`
    <style>
        :host{
            @apply --vertical;
        }

        :host *:not(style){
            @apply --flex;
        }
        :host oda-button {
            margin: 2px;
            @apply --raised;
        }
        :host oda-button.requestBtn{
            background-color: var(--accent-color);
            color: var(--content-background);
            fill: var(--content-background);
        }
        :host([requesting]) oda-button.requestBtn {
            @apply --disabled;
        }
        :host([requesting][completed]) oda-button.requestBtn {
            @apply --disabled;
            background-color: var(--success-color);
            filter: unset;
            opacity: 0.5;
        }
        @media(max-width:270px){
            .container{
                font-size: 60%;
            }
        }
    </style>
    <div class="container vertical flex" style="padding: 8px;">
        <p ~html="text"></p>
        <P ~html="resultText"></p>
        <div class="horizontal">
            <oda-button class="requestBtn" @tap="request" :icon="requestBtnIcon" ~text="requestBtnText" icon-pos="right" ~style="{minHeight: iconSize + 10 + 'px'}"></oda-button>
            <oda-button @tap="switchAcc">switch accounts</oda-button>
            <oda-button ~if="!getIsGuest()" @tap="goHome" icon="shopping:home"></oda-button>
        </div>
    </div>
  `,
    iconSize: 24,
    contextItem: null,
    accessRequests: undefined,
    resultText: '',
    $public: {
        requesting: {
            $def: false,
            $attr: true
        },
        completed: {
            $def: false,
            $attr: true
        }
    },
    get text() {
        const context = typeof this.contextItem === 'string' ? this.contextItem : this.contextItem.typeLabel.toCamelCase();
        return `You need permissions to access <b>${context}</b>.`;
    },
    get requestBtnIcon() {
        return this.completed
            ? 'icons:check'
            : this.requesting ? 'loaders:spin' : '';
    },
    get requestBtnText() {
        return this.completed
            ? 'request was sent successfully'
            : this.requesting ? 'request in progress' : 'request access';
    },
    getIsGuest() {
        const login = Auth.getSecurity()?.login;
        return !login || login.toLowerCase() === 'guest';
    },
    attached() {
        this.checkRequests();
        // this.domHost.style.setProperty('z-index', '2');
        setInterval(() => {
            this.checkRequests();
        }, 1000)
    },
    auth() {
        this.requesting = true;
        ODA.top.location.assign(`/web/auth/login.html?redirect_url=${location.href}`);
    },
    async request() {
        const { $item, context } = (() => {
            if (typeof this.contextItem === 'string') {
                return {
                    $item: CORE.host,
                    context: this.contextItem
                }
            }
            else {
                return {
                    $item: this.contextItem,
                    context: this.contextItem.path
                }
            }
        })();
        if (this.getIsGuest()) {
            ODA.top.ODANT.showWelcome(true, $item);
            this.containerHost.allowClose = true;
            this.containerHost.fire('cancel');
        } else {
            this.requesting = true;
            const res = await this.contextItem.$base.executeServerHandler('request-access', { context });
            this.resultText = res.replace(/{{USER_EMAIL}}/, Auth.getSecurity().login);
            this.setRequests(this.resultText);
            this.completed = true;
        }
    },
    switchAcc() {
        (this.contextItem?.$host || CORE.host)?.logout();
    },
    checkRequests() {
        this.getRequests();
        const info = this.accessRequests?.[Auth.getSecurity().login]?.[location.href];
        const date = info?.date ?? 0;
        if (date && (date + 1000 * 60 * 60 * 24) > Date.now()) {
            this.requesting = true;
            this.completed = true;
            this.resultText = info.text;
        } else {
            this.requesting = false;
            this.completed = false;
        }
    },
    getRequests() {
        this.accessRequests = JSON.parse(localStorage.getItem('accessRequests') || '{}');
        return this.accessRequests;
    },
    setRequests(text = '') {
        const requests = this.getRequests();
        requests[Auth.getSecurity().login] = requests[Auth.getSecurity().login] || {};
        requests[Auth.getSecurity().login][location.href] = {
            date: Date.now(),
            text
        };
        localStorage.setItem('accessRequests', JSON.stringify(requests));
    },
    async goHome() {
        CORE.host.open('navigator');
        // CORE.host.$user.open();
    },
    show(contextItem) {
        if (contextItem) {
            this.contextItem = contextItem;
        }
        const context = typeof this.contextItem === 'string' ? this.contextItem : this.contextItem.typeLabel.toCamelCase();
        return ODA.showModal(this, {}, {title: `Access to ${context}`,  allowClose: false });
    }
})