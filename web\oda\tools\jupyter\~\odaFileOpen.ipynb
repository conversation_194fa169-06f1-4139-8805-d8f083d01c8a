{"cells": [{"cell_type": "code", "source": ["host = this;\ncontent = 'мама мыла раму';\nODA({is: 'oda-file-open',\n    template: `\n        <style>\n            :host{\n                @apply --vertical;\n            }\n        </style>\n        <h2>\n            Загрузка текстового файла\n        </h2>\n        <input accept=\".txt\" type=\"file\" @change=\"readFile\">\n    `,\n    content: {\n        $def: true,\n        $attr: true\n    },\n    readFile(e){\n        let file = e.target.files[0];\n        if (!file) \n            return;\n        let reader = new FileReader();\n        reader.onload = function(e) {\n            console.log('oda-file-open');\n        };\n        reader.readAsText(file);\n    }\n})\n>'oda-file-open'\ntree = this.createElement('oda-file-open', {\n\n}) \n>tree"], "metadata": {"id": "130575fc609"}, "time": "1 ms", "outputs": [{"data": {"text/plain": "<div style='cursor: pointer' onclick='_findCodeEntry(this)'>oda-file-open</div>"}}, {"data": {"text/plain": "<label bold onclick='_findCodeEntry(this)' style='text-decoration: underline; padding: 2px; font-size: large; margin-bottom: 4px; cursor: pointer; color: -webkit-link'>tree</label>"}}, {"data": {"html/text": {}}}]}], "hiddenErrors": []}