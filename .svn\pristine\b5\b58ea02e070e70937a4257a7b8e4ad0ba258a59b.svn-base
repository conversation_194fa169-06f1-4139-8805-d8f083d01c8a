export default {
    label: 'Open form',
    icon: 'icons:apps',
    get openUrl() {
        return `${this.contextItem.url}/~/client/pages/${this._path}/index.html`;
    },
    get allowUse() {
        return !(this.contextItem instanceof odaHandler);
    },
}
MAIN({extends: 'oda-app-layout',
    imports: [
        '@oda/app-layout',
        '@lib/action-bar'
    ],
    template: /*html*/`
    <style>
        ::slotted([slot=main]) {
            overflow: auto;
        }
        {{''}}
    </style>
    <odant-form-title ~if="view" slot="title-bar" :icon-size :context-item style="overflow: hidden; text-overflow: ellipsis;"></odant-form-title>
    <app-layout-toolbar :icon-size="iconSize*1.5" slot="top" class="dark" >
        <div slot="top-left" class="horizontal flex between" style="align-items: flex-end; overflow: hidden;" @contextmenu="_topContextMenu">
            <odant-action-bar show-border ~show="allowButtons" bar-type="tool-bar" :icon-size="iconSize*1.5" groups="button" :context-item="focusedClass || contextItem" style="margin-right: 4px;"></odant-action-bar>
            <odant-action-bar show-border ~show="allowButtons" bar-type="tool-bar" ~if="focusedItem && focusedItem !== focusedClass && focusedItem !== contextItem" :icon-size="iconSize*1.5" groups="button" :context-item="focusedItem"></odant-action-bar>
        </div>
    </app-layout-toolbar>
    <app-layout-toolbar :icon-size="iconSize*1.5" slot="footer" class="dark" style="order: 100;">
        <div slot="footer-left" class="horizontal flex" style="margin-left: 40px;">
            <odant-action-bar show-border bar-type="service-bar" no-flex  allow-labels :icon-size="iconSize*1.5"  groups="service" :context-item="focusedClass || contextItem" style="margin-right: 4px;"></odant-action-bar>
            <odant-action-bar show-border bar-type="service-bar" ~if="focusedItem && focusedItem !== focusedClass && focusedItem !== contextItem" allow-labels :icon-size="iconSize*1.5" groups="service" :context-item="focusedItem"></odant-action-bar>
        </div>
        <div class="no-flex horizontal float" slot="footer-right" style="position: absolute; bottom: 0px; right: 0px;">
            <style>
                oda-button.footer-tool{
                    margin: 2px 4px;
                    border-radius: 4px;
                }
            </style>
            <oda-button
                id="errorBtn"
                ~show="errors.length > 0"
                class="footer-tool raised no-flex error"
                icon="icons:error-outline"
                :bubble="errors.length"
                @tap="_showErrors"
            ></oda-button>
            <oda-button title="OK: save & close" shadow border ~if="float" class="footer-tool raised bold content no-flex" ~style="{minHeight: iconSize + 'px'}" icon="fontawesome:s-circle-check" success-invert :icon-size @tap="_ok"></oda-button>
        </div>
    </app-layout-toolbar>`
    ,
    hostAttributes: {
        tabindex: 0
    },
    $public: {
        $pdp: true,
        get saveIcon() {
            return 'icons:save';
        },
        get savingIcon() {
            return 'spinners:wind-toy';
        },
        allowCompact: false,
        viewProps: {},
        animated: true,
        allowButtons: {
            $type: Boolean,
            /** @this {odantForm} */
            get() {
                return !!this.view?.allowButtons;
            }
        },
        hideViews: Boolean,
        allowClose: {
            get() {
                const result = window.opener || !this.contextItem;
                if (!result) {
                    /** @type { odantComponent } */
                    //@ts-ignore
                    const topPage = ODA.top.document.body.firstElementChild;
                    return topPage.contextItem.path !== this.contextItem.path || (this.contextItem.defaultView !== this.viewId);
                }
                return !!result;
            }
        },

        settingsId: {
            $type: String,
            get() {
                if (this.contextItem && this.view)
                    return `${(this.contextItem.$class || this.contextItem).path.slice(1)}/${this.view.id}`;
                return this.localName;
            }
        },
    },
    $pdp: {
        hidden: {
            $attr: true,
            $def: false
        },
        get form() {
            return this;
        },
        get focusedItem() {
            return this.control?.focusedItem;
        },
        set focusedItem(v) {
            this.$render();
        },
        get focusedClass() {
            return this.contextItem;
        },
        set focusedClass(n) {

        },
        isChanged: {
            $type: Boolean,
            get() {
                this._closeDialog = undefined;
                return this.control?.isChanged;
            }
        },
        isMainForm: {
            get() {
                return this.parentElement?.firstElementChild === this;
            }
        },
        viewId: '',
        view: {
            $type: Object,
            /**
             * @this {odantForm}
             * @param {odaView} view
             */
            async set(view, o) {
                this.control = undefined;
                if (!view) {
                    return;
                }
                if (o) {
                    this.previousView = o;
                }
                let control;
                try {
                    control = await view.control;
                    if (control.currentForm && control.currentForm !== this) {
                        control = await view.createComponent(view.tagName, { contextItem: view.$context, $handler: view });
                    }
                }
                catch (err) {
                    control = ODA.createComponent('odant-error-view', { contextItem: this.contextItem, $view: view });
                    console.warn(err);
                }
                this.control = control;
                if (!this.float) {
                    this.fire('nav-view-changed', { view, previousView: o });
                    if (window === window.top) {
                        window.location.hash = `view=${view.id}`;
                    }
                }
            },
        },
        control: {
            $type: HTMLElement,
            async set(n, o) {
                this['#autoSave'] = undefined;
                [n, o] = await Promise.all([n, o]);
                if (o) {
                    this.unlisten('is-changed-changed', 'onIsChanged', { target: o });
                    this.unlisten('auto-save-changed', 'onAutoSaveChanged', { target: o });
                    o.remove();
                    o.layoutHost = undefined;
                    o.currentForm = undefined;
                    this.leftButtons = [];
                }
                if (n) {
                    this.listen('is-changed-changed', 'onIsChanged', { target: n });
                    this.listen('auto-save-changed', 'onAutoSaveChanged', { target: n });
                    this.debounce('control-changed', () => {
                        n.layoutHost = this.layoutHost;
                        n.currentForm = this;
                        this.appendChild(n);
                        n.assignProps(this.viewProps);
                        n.setProperty('slot', 'main');
                    }, 100);
                }
                this.async(() => {
                    this.focusedItem; // todo: Костыль для панели handler'ов фокусного объекта в таблице
                    this.async(() => {
                        this.style.removeProperty('visibility');
                    }, 200);
                }, 100);
            }
        },
        previousView: null,
        saving: false,
        closing: false,
    },
    autoSave: {
        $public: true,
        $type: Number,
        get() {
            return !!(this.view?.autoSave ?? this.control?.autoSave);
        },
        set(n) {
            if (n) {
                this.saveAuto();
            }
        }
    },
    errors: [],
    _closeDialog: false,
    /**@this {odantForm} */
    get $saveKey() {
        return this.contextItem?.typeName;
    },
    /**@this {odantForm} */
    get needConfirmClose() {
        return this.view?.allowSave && !this.contextItem?.readOnly && (this.isChanged || this.contextItem.isChanged);
    },
    childForms: [],
    leftButtons: {
        get() {
           return this.control?.leftButtons || [];
        }
    },
    $keyBindings: {
        "ctrl+s"(e) {
            e.preventDefault();
            e.stopPropagation();
            this.save();
        }
    },
    $observers: {
        async loadView(focusedClass, viewId) {
            if (focusedClass === null) return;
            if (!focusedClass.body && !(focusedClass instanceof odaFile)) {
                await focusedClass.load();
            }
            if (this.control?.load) {
                console.warn('ОБНАРУЖЕН КОСТЫЛЬ load', this.control);
                await this.control?.load?.();
            }

            if (!viewId) {
                this.hashchange();
            }
            viewId = this.viewId;
            if (!viewId) {
                viewId = this.viewId = focusedClass.defaultView;
            }
            try {
                this.view = await focusedClass.getHandler(viewId);
            }
            catch (err) {
                console.warn(err);
            }
            if (!this.view) {
                const handlers = await focusedClass.$$handlers;
                const views = handlers.filter(i => i.typeLabel === 'view');
                this.view = views.find(a => a.name === viewId) || views[0];
            }
            this.unlisten('delete', '_close', { target: focusedClass });
            this.listen('delete', '_close', { target: focusedClass });
            this.unlisten('is-changed-changed', 'onIsChanged', { target: focusedClass });
        },
        isChangedChanged(isChanged) {
            if (isChanged) {
                this.listen('beforeunload', '_beforeUnload', { target: window });
                this.saveAuto();
            }
            else {
                this.unlisten('beforeunload', '_beforeUnload', { target: window });
            }
        }
    },
    /**@this {odantForm} */
    async attached() {
        this.$super('oda-app-layout', 'attached');
        if (!ODA.services.getService('form')) {
            ODA.services.registerService('form', this);
        }
        if (this.parentElement === document.body && window.top === window && !(this.contextItem instanceof odaObject)) {
            this.showTitle = true;
        }
        this.hashchange();
        this.listen('hashchange', 'hashchange', { target: window });
        this.listen('keydown', '_onKeyDown', { capture: true });
    },
    /**@this {odantForm} */
    detached() {
        if (ODA.services.getService('form') === this) {
            ODA.services.unregisterService('form');
        }
        this.$super('oda-form-layout', 'detached');
        this.unlisten('hashchange', 'hashchange', { target: window });
        this.unlisten('keydown', '_onKeyDown', { capture: true });
    },
    /**@this {odantForm} */
    hashchange() {
        if (!this.isMainForm) {
            return;
        }
        let idx = window.location.hash.lastIndexOf('#defaultView=');
        if (~idx) {
            this.viewId = window.location.hash.slice(idx + '#defaultView='.length);
            return;
        }
        idx = window.location.hash.lastIndexOf('#view=');
        if (~idx) {
            this.viewId = window.location.hash.slice(idx + '#view='.length);
            return;
        }
    },
    /**@this {odantForm} */
    ready() {
        this.style.setProperty('visibility', 'hidden');
    },
    /**@this {odantForm} */
    onIsChanged(e) {
        this._closeDialog = undefined;
        this.isChanged = undefined;
        //this.isChanged = e.detail.value;
    },
    /**@this {odantForm} */
    onAutoSaveChanged(e) {
        this.autoSave = e.detail.value;
    },
    /**@this {odantForm} */
    _topContextMenu(e) {
        this.contextItem.showMenu({ contextItem: this.contextItem, groups: '*', collapseLimit: 3 });
    },
    /**@this {odantForm} */
    _beforeUnload(e) {
        e.preventDefault();
        return (e.returnValue = 'stop');
    },
    /**@this {odantForm} */
    async _showView($item, viewId, dialog) {
        $item = await this.contextItem.findItem($item.path);
        const $view = await $item.getHandler(viewId);

        if (!dialog && $item.path === this.contextItem.path) {
            this.view = $view;
            this.viewId = this.view.id;
            this._top();
        } else {
            let form = Array.from(this._getAllForms()).find(f => f.contextItem.path === $item.path);
            if (form && !dialog) {
                form._top();
            } else {
                form = ODANT.createComponent.call(this, 'odant-form');
                form.contextItem = $item;
                form.$saveKey = undefined;
                this.addChildForm(form);
                if(dialog) form.dialog = true;
            }
            if (!form.view || (form.view.id !== viewId)) {
                form.view = $view;
                form.viewId = form.view.id;
            }
            form.sidePadding += this.sidePadding;
            return form.show();
        }
    },
    addChildForm(form) {
        if (!this.childForms.includes(form)) {
            this.childForms.push(form);
            this.listen('form-close', ()=> this.removeChildForm(form), { target: form, once: true });
        }
    },
    removeChildForm(form) {
        const idx = this.childForms.indexOf(form);
        if (~idx) {
            this.childForms.splice(idx, 1);
            if(!form.dialog){
                this._top();}
        }
    },
    /**@this {odantForm} */
    navigateTo($item) {
        const parentPage = window.parent?.document?.body?.firstElementChild;
        if (typeof parentPage?.navigateTo === 'function' && parentPage !== this) {
            parentPage.navigateTo($item);
        }
    },
    /**@this {odantForm} */
    show() {
        this.float = true;
        this.$super('oda-form-layout', 'show');
        const result = new Promise((resolve, reject) => {
            this.resolve = resolve;
            this.reject = reject;
            this.addEventListener('dialog-form-result', e => {
                e.stopPropagation();
                Promise.resolve(this.control)
                    .then(res => resolve(res))
                    .catch(err => console.warn(err))
                    .finally(() => this._close());
            });
        });
        if (this.hidden) {
            this.hidden = false;
            this._top();
            return result;
        }
        document.body.appendChild(this);
        this.style.setProperty('z-index', '0');
        this.style.setProperty('visibility', 'hidden');
        const h = () => {
            this.debounce('resize-visibility', () => {
                this.unlisten('resize', h);
                this.style.removeProperty('visibility');
            }, 100);
        };
        this.listen('resize', h);
        return result;
    },
    /**@this {odantForm} */
    showDialog($item, viewId) {
        return this._showView($item, viewId, true);
    },
    /**@this {odantForm} */
    async save() {
        if (!this.view?.allowSave || this.contextItem.readOnly) return;
        try {
            this.saving = true;
            await this.control?.save?.();
            // console.log('saved');
        }
        catch (err) {
            console.warn(`Error on saving ${this.contextItem.typeLabel} "${this.contextItem.label}"\n`, err);
            this._pushErrors(err);
        }
        finally {
            this.saving = false;
            this.saveIcon = undefined;
        }
    },
    close() {
        return this._close();
    },
    /**@this {odantForm} */
    async _close() {
        if (this._closeDialog)
            return;
        this._closeDialog = true;
        if (this.needConfirmClose) {
            try {
                const buttons = [
                    {
                        label: 'Yes',
                        style: { 'color': 'var(--success)' },
                        left: true,
                        tap: () => {
                            return this.save();
                        }
                    },
                    {
                        label: 'No',
                        tap: async () => {
                            this.contextItem.resetCache('on-load');
                            if (!this.contextItem.isNew) {
                                this.contextItem.body = await this.contextItem.load();
                                this.contextItem.isChanged = false;
                            }
                        }
                    }
                ];
                await this.contextItem.showConfirm('Save before close?', { icon: 'icons:save', buttons, fixWidth: true, hideOkButton: true });
            } catch (err) {
                this._closeDialog = false;
                return false;
            }
        }
        this.reject?.();
        if (this.float) {
            this.remove();
        }
        else if (this.parentElement === document.body && (window.parent === window || window.opener)) {
            this.async(() => window.close(), 100);
        }
        this.fire('form-close');
        this._closeDialog = false;
        return true;
    },
    hide() {
        this.hidden = true;
        this.childForms.forEach(f => f.hide());
    },
    /**@this {odantForm} */
    saveAuto() {
        if (!this.autoSave || !this.isChanged) return;
        this.debounce('auto-save', () => {
            this.save();
        }, this.autoSave > 300 ? this.autoSave : 300);
    },
    /**@this {odantForm} */
    async _ok() {
        if ((this.control?.isChanged || this.contextItem?.isChanged) && !this.contextItem?.readOnly) {
            await this.save();
        }
        if (this.resolve) {
            this.resolve(this.control);
        }
        this._close();
    },
    /**@this {odantForm} */
    _onKeyDown(e) {
        switch (e.key) {
            case 'Escape': {
                if (this.dialog && this.float) {
                    this._close();
                }
            } break;
            case 's': {
                if (e.ctrlKey) {
                    e.preventDefault();
                    return this.save();
                }
            } break;
        }
    },
    /**@this {odantForm} */
    _pushErrors(err) {
        this.errors.push(err);
        this._showErrors();
    },
    /**@this {odantForm} */
    _showErrors() {
        ODA.showDropdown('oda-error-list', { errors: this.errors }, { title: 'Errors', parent: this.$('#errorBtn')});
    },
    _top() {
        this.async(() => {
            this.$super('oda-form-layout', '_top');
            if (this.hidden) {
                this.hidden = false;
            }
            this.childForms.forEach(f => f.hide());
        }, 100);
    },
});

ODANT({
    is: 'odant-error-view',
    template: /*html*/ `
    <style>
        :host{
            @apply --horizontal;
            align-items: center;
            justify-content: center;
        }
        .error-message{
            @apply --error;
            margin-bottom: 8px;
            white-space: pre;
            padding: 8px;
            outline: 1px solid;
            outline-offset: -4px;
        }
    </style>
    <div class="vertical flex" style="align-items: center; justify-content: center;">
        <span class="error-message" ~text="_getText()"></span>
        <oda-button ~if="$view" class="raised" icon="icons:build" label="Go to fix it" @tap="_tap" style="padding: 8px;"></oda-button>
    </div>`,
    $public: {
        $view: Object
    },
    _getText() {
        if (this.$view)
            return `Error in "${this.$view.label}" view!\n\t${this.$view.error}`;
    },
    async _tap() {
        this.$view.executeHandler('edit-code');
    }
});
ODANT({
    is: 'odant-form-title', imports: '@oda/menu, @lib/item-node',
    template: /*html*/`
        <style>
            :host {
                flex-wrap: wrap;
                justify-content: space-between;
                @apply --flex;
                @apply --horizontal;
                background-color: transparent !important;
                min-height: {{titleHeight}}px;
            }
            {{''}}
        </style>
        <odant-item-node-explorer
            wrap
            expanded
            expand-to-prefix="C"
            fill="var(--content-background)"
            :context-item
            :icon-size
            style="max-width: 100%; width: 100%; background-color: transparent;"
            class="no-flex"
            :allow-edit-color="allowEditNode"
            :allow-edit-label="allowEditNode"
        >
            <div class="flex"></div>
            <div class="horizontal flex" style="align-items: center; justify-content: right;">
                <slot name="title-buttons"></slot>
                <oda-button ~if="previousView" icon="icons:arrow-back" @tap="previousView.execute()"></oda-button>
                <odant-view-selector
                    ~show="_getShowViews(hideViews, view)"
                    style="margin: 0px 4px;"
                    :context-item
                    :view
                    :icon-size="~~(iconSize * 0.99)"
                    @down="_down"
                ></odant-view-selector>
                <odant-action-bar bar-type="quick-tool" :context-item :icon-size></odant-action-bar>
            </div>

        </odant-item-node-explorer>

    `,
    get titleHeight() {
        return this.iconSize + this.iconSize / 2 + 4;
    },
    get allowEditNode() {
        return (this.contextItem instanceof odaObject) && this.contextItem.access.allow('write');
    },
    _down(e) {
        this._downEvent = e.detail.sourceEvent;
    },
    _getShowViews(hideViews, view) {
        if (view && !hideViews) return !view.hideViews;
        return !hideViews;
    },
    showSettings: noDragWrap(async function (e) {
        try {
            await ODA.import('@tools/property-grid');
            await ODA.showDropdown(
                'oda-property-grid',
                { inspectedObject: this.control, onlySave: true, style: 'max-width: 500px; min-width: 250x;', filterByFlags: '$save' },
                { parent: e.target, anchor: 'top-right', align: 'left', title: 'Settings', hideCancelButton: true }
            );
        }
        catch (e) { }
    }),
    openInNew: noDragWrap(function (e) {
        const viewParam = !this.view || this.view.id === this.contextItem.defaultView
            ? ''
            : `#view=${this.view.id}`;
        window.open(`${this.contextItem.url}/~/client/pages/form/index.html${viewParam}`, '_blank');
    }),
    $listeners: {
        resize() {
            this.minWidth = (this.$('[slot=title-buttons]')?.offsetWidth || 300) + this.iconSize * 6;
        }
    }
});

function _copyToClipboard(string) {
    const input = document.createElement('input');
    input.style.opacity = '0';
    input.style.display = 'fixed';
    document.body.appendChild(input);
    input.value = string;
    input.focus();
    input.select();
    document.execCommand("Copy");
    input.remove();
}
function noDragWrap(f) {
    return function (e, ...args) {
        const se = e.detail.sourceEvent;
        if (this.sizeMode === 'max' || !this.float || (!this._downEvent || se.x === this._downEvent.x && se.y === this._downEvent.y)) {
            return f.call(this, e, ...args);
        }
        this._downEvent = undefined;
    };
}
ODA({
    is: 'odant-form-close-confirm',
    template: /*html*/`
    <style>
        :host {
            padding: 16px;
            /*font-size: 150%;*/
        }
    </style>
    <oda-icon icon-size="64" :icon="contextItem?.icon" ></oda-icon>
    <div ~html="text"></div>
    `,
    contextItem: null,
    get text() {
        return `Save<p><b>${this.label}</b><p> before closing?`;
    },
    get label() {
        const item = this.contextItem;
        if (item) {
            return `${item.typeLabel}: "${item.label || item.name || item.id}"`;
        }
        return 'item ';
    }
});
ODANT({
    is: 'odant-view-selector', imports: '@oda/button',
    template: /*html*/`
    <style>
        :host {
            border-radius: 4px;
            align-items: center;
            @apply --horizontal;
            @apply --no-flex;
            @apply --header;
            cursor: pointer;
            overflow: hidden;
        }
        oda-button {
            font-size: inherit;
        }
    </style>
    <style>
        :host{
            font-size: {{iconSize * .65}}px;
        }
        {{''}}
    </style>
    <oda-button :icon="saving ? savingIcon : saveIcon" ~if="view?.allowSave && !contextItem?.readOnly" :disabled="saving || !isChanged" success-invert @tap.stop="save" style="border-radius:4px; margin: 1px;" :icon-size title="Save..."></oda-button>
    <oda-button ~if="!dialog" @tap.stop="reload" style="padding: 4px; border-radius: 50%;" :icon-size title="Refresh..." icon="icons:autorenew"></oda-button>
    <odant-item-node hide-icon bold :context-item="view" :icon-size="childIconSize" style="margin: 0px 6px; align-self: center; padding: 0px;"></odant-item-node>
    <odant-action-bar style="align-self: center;" :icon-size="childIconSize" bar-type="quick-tool" :context-item="view"></odant-action-bar>
    <oda-button ~if="!disableSettings" style="padding: 4px; border-radius: 50%" title="Settings..." :icon-size="childIconSize" icon="icons:settings" @down.stop class="no-flex" @tap.stop="showSettings" :icon-size></oda-button>
    <oda-button style="padding: 3px; border-radius: 10%" :icon-size title="Select view..." icon="icons:chevron-right:90"></oda-button>
    `,
    get childIconSize() {
        return ~~(this.iconSize * .7);
    },
    get isChanged() {
        return this.form?.isChanged;
    },
    save(){
        return this.domHost.domHost.save();
    },
    reload(){
        window.location.reload();
    },
    get disableSettings() {
        // todo: понаблюдать
        if (!this.control) return;
        if (!this.control.constructor.__rocks__) return [];
        return Object.entries(this.control.constructor.__rocks__.descrs).filter(d => d[1].$save).length === 0;
    },
    view: null,
    $listeners: {
        async tap() {
            const { control } = await this.contextItem.showMenu(
                { contextItem: this.contextItem, parent: this, groups: 'view', focusedView: this.view, title: 'Select view...' },
                { parent: this, align: 'left', anchor: 'top-right' }
            );
            if (!control.focusedItem?.quickTool && control.focusedItem)
                this.view = control.focusedItem;
        }
    },
});


ODANT({is: 'odant-hidden-check', imports: '@oda/table', extends: 'oda-table-check',
    get icon() {
        return this.value ? this.iconUnchecked : this.iconChecked;
    },
    get value() {
        return this.hiddenItems.some(i => i === this.item.$item.id);
    },
    _toggleChecked(e) {
        const idx = this.hiddenItems.indexOf(this.item.$item.id);
        if (~idx) {
            this.hiddenItems.splice(idx, 1);
        }
        else {
            this.hiddenItems.push(this.item.$item.id);
        }
        this.hiddenItems = [...this.hiddenItems];
    },
});

ODA({
    is: 'oda-error-list',
    template: /*html*/`
        <style>
            :host{
                @apply --vertical;
                @apply --flex;
                overflow: hidden;
            }
            :host .container{
                @apply --flex;
                @apply --vertical;
                overflow: auto;
            }
            :host .header-bar{
                @apply --horizontal;
                @apply --flex;
                @apply --raised;
                align-items: center;
                padding: 2px;
                margin-bottom: 2px;
            }
            :host .block{
                margin: 2px;
                padding: 2px;
                border: 1px solid gray;
                @apply --warning;
                @apply --raised;
            }
            :host .block-title{
                @apply --horizontal;
                @apply --flex;
                @apply --error;
                align-items: center;
            }
            :host .block .block-header  oda-button[icon="bootstrap:trash"]{
                justify-self: flex-end;
            }
            :host .block .block-body{
                white-space: pre;
                overflow: auto;
                padding: 8px;
            }
        </style>
        <div class="horizontal flex" style="align-items: center; padding: 2px; margin-bottom: 2px; flex-direction: row-reverse;" slot="modal-title">
            <oda-button class="no-flex" icon="carbon:clean" @tap="_removeAll()"></oda-button>
        </div>
        <div class="container">
            <div ~for="_formattedErrors" class="block">
                <details @tap.prevent>
                    <summary class="block-title">
                        <oda-icon
                            @tap.prevent.stop="$this.parentElement.parentElement.open = !$this.parentElement.parentElement.open"
                            :icon="$this.parentElement.parentElement.open ? 'icons:chevron-right:90' : 'icons:chevron-right'"
                        ></oda-icon>
                        <div class="flex" style="padding-right: 8px;">{{$for.item.title}}</div>
                        <oda-button icon="bootstrap:trash" @tap="_remove($for.item.error)"></oda-button>
                    </summary>
                    <div class="block-body">
                        {{$for.item.error.message}}
                    </div>
                </details>
            </div>
        </div>
    `,
    errors: [],
    _formattedErrors: {
        get() {
            return this.errors.map(e => {
                return {
                    title: e.message.slice(0, e.message.indexOf('\n')),
                    error: e
                }
            })
        }
    },
    $observers: {
        '_errorsChanged': 'errors'
    },
    _errorsChanged() {
        this.containerHost.title = `Errors (${this.errors.length})`;
    },
    _remove(item) {
        this.errors.splice(this.errors.indexOf(item), 1);
        if (this.errors.length === 0) {
            this.fire('cancel');
        }
        this._errorsChanged();
    },
    _removeAll(){
        this.errors.splice(0, this.errors.length);
        this.fire('cancel');
    }
});