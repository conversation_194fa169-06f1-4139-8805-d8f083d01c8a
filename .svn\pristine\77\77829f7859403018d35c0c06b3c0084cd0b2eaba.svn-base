 export default {
    order: 1100,
    icon: 'fontawesome:r-folder-open',
    allowUse: true,
    get openUrl() {
        if ((this.$source === this.contextItem)
                || (this.contextItem instanceof odaObject)
                || (this.contextItem instanceof odaFile)) {
            return `${this.contextItem.url}/~/client/pages/${this._path}/index.html`;
        }

        throw new Error('Wrong way');
    }
}
PAGE({ extends: 'odant-navigate-layout', imports: '@lib/navigate-layout',
    contextItem: {
        async set(n) {
            if (n) {
                this.contextItem = n instanceof odaFile
                    ? n
                    : await n.$folder;
            }
        }
    },
    get $saveKey() {
        if (!this.contextItem) return;
        return `${this.contextItem.path}/type=${this.contextItem.typeName}`;
    },
    attached() {
        this.$super('odant-navigate-layout', 'attached');
        this.panels[0].opened ??= true;
        ODA.waitReg('odant-structure-tree').then(() => {
            this.structureTree.attachmentsMode = true;
            this.structureTree.hideTop = false;
        });
    }
});