import * as Auth from '/web/auth/auth.js';
export default {
    icon: 'brands:webauthn',
    get allowUse() {
        return (!localStorage.getItem('security') || CORE.host.$user?.isGuest);
    },
  async execute() {
        try {
            const { control } = await this.showDialog('oda-login-form');
            const login = control.login || '';
            const accounts = await Auth.findAccount(login);
            if (Array.isArray(accounts)) {
                // todo делаем по-новому - ПРАВИЛЬНО!!!
            }
            else {
                const authUrl = new URL(`${accounts}/web/auth/pap/index.html`);
                const url = new URL(ODA.top.location.toString());
                const redirect_url = url.searchParams.get('redirect_url') || ODA.top.location.toString();
                authUrl.searchParams.set('login', login);
                authUrl.searchParams.set('redirect_url', redirect_url);
                ODA.top.location.assign(authUrl);
            }
        }
        catch (err) {
          console.error(err);
          const userDaft = { login: 'guest' };
          Auth.setSecurity(userDaft);
        }
    }
}
ODA({
    is: 'oda-login-form',
    template: `
      <style>
        :host{
            @apply --vertical;
        }
        fieldset{
            margin: 8px;
            border-radius: 4px;
        }
        input{
          border: none;
          outline: none !important;
          font-size: large;
          width: 0px;
          @apply --flex;
        }
      </style>
      <form id="form" class="vertical">
        <fieldset class="horizontal">
          <legend>
            <label for="login">Login</label>
          </legend>
          <input id="login" autofocus placeholder="Enter e-mail or phone number..." ::value="login">
        </fieldset>
      </form>
    `,
    attached() {
        this.async(() => {
            this.$('input').focus();
        })
    },
    login: undefined
})