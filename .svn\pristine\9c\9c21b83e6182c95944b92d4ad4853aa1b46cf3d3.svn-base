import {torus} from "../../torus.js";
import {Linear, nn} from "../neuro-module.js";
export class Tokenizer extends nn.NeuroModule{
    stats = {};
    constructor(dim = 16, char_step = 3, win_size = 10, negative_size = 5, lex_separator = '_') {
        super(...arguments);
    }
    get targetSize(){
        return this._targetSize ??= this.win_size * (this.negative_size + 1);
    }
    get TARGET(){
        return this._TARGET ??= (()=>{
            const _bins =  Array(this.win_size).fill(1).map((v, i)=>(2. ** - (i + 1) + .5));
            while (_bins.length < this.targetSize)
                _bins.push(0);
            return torus.from(_bins);
        })();
    }
    __init__(){
        this.vocabulary = {
            "<start>": {w: "<start>", start: true,
                emb: torus.ones(this.dim)._label('emb: <start>'),
                cnt: torus.rand(this.dim)._label('cnt: <start>')
            },
            "<stop>": {w: "<stop>", stop: true, id: 0,
                emb: torus.zeros(this.dim)._label('emb: <stop>'),
                cnt: torus.rand(this.dim)._label('cnt: <stop>')
            }
        }
        this.logit = torus.param(torus.zeros([100, this.dim]).float());
    }
    predict(data, token, target){
        data = torus.from(data);
        let logit = this.logit;
        if (token?.lex && token.stat?.length > 0){
            logit = token.logit;
            if (target){
                target = token.stat.indexOf(target.t)
                if (token.stat?.length === 1)
                    return target;
            }
        }
        else if (target){
            token = target
            target = target?.id
        }

        logit = torus.einsum('x, lx->l', [data,  logit]);
        if (target !== undefined){
            let loss = torus.cross_entropy(logit, target);
            this.losses.add(loss)
            loss.back();
        }
        else{
            let probs = logit.softmax(-1);
            const idx_next = probs.multinomial(1);
            token = token.stat[idx_next];
            if (token){
                token = this.vocabulary[token];
            }
            else{
                token = this.logit_tokens[idx_next];
            }
        }
        return token;
    }
    decode(tokens, word){
        if (word){
            let before = this.tokenize(word);
            tokens.unshift(...before.map(t => t.emb.data));
            word = '';
        }
        return tokens.map(data =>{
            if (word){
                let stat = this.vocabulary[word]?.stat;
                if (stat){
                    let stats = this.stats[word] ??= (()=>{
                        let stats = [];
                        let summary = 0;
                        for (let w in stat){
                            let token = this.vocabulary[w];
                            let count = stat[w];
                            stats.push({token, count})
                            summary += count ** 2;
                        }
                        summary = Math.sqrt(summary);
                        stats.forEach(stat=>{
                            stat.count /= summary;
                        })
                        return stats;
                    })()
                    let found = stats.map(stat =>{
                        let token = stat.token;
                        return {
                            token, s: torus.cos_similar(token.emb.data, data) /** stat.count*/, t: token.t
                        }
                    })
                    found = found.sort((a,b) => a.s>b.s?-1:1);
                    word = found[0]?.w;    // todo multinomial
                    word = word.replace('##', '')
                    return word;
                }
            }
            let found = this.tokens.map(token =>({
                    token, s: torus.cos_similar(token.emb.data, data), w: token.t
                }))
            found = found.sort((a,b) => a.s>b.s?-1:1);
            word = found[0]?.t;    // todo multinomial
            return word;
        }).join('')
    }
    get tokens_error(){
        const size = this.size;
        return this['#tokens_error'] ??= (()=>{
            const tokens = this.tokens.filter(i=>(i.error>0 && i.error<1))

            if (!size)
                return 1;
            let error = tokens.reduce((r, t) =>{
                return r + t.error;
            }, 0)
            error /= size;
            return  error;
        })()
    }
    get error(){
        return this.tokens.filter((_,i)=>i).map(i=>i.error).avg();
    }
    set progress(n){
        this.onProgress(n)
    }
    async onProgress(progress){

    }
    async train(text){
        let tokens = this.tokenize(text);
        let win_size = this.win_size;
        let size = this.targetSize;

        // words = words.filter(t => /[а-яА-Яa-zA-ZЁё]|\n/.test(t.slice(-1)));
        let prev_token = null;
        let paragraphs = tokens.reduce((res, token)=>{
            if(token.t === '\n'){
                if(res.last.length)
                    res.push([]);
                prev_token = null;
            }
            else if (token.lex){
                prev_token = this.add_token(token.t, prev_token);
                res.last.push(prev_token);
            }
            return res;
        }, [[]]);


        // let paragraphs = this.tokenize(text, true);
        this.progress = 0;
        try{
            let time = Date.now();
            let p_length = paragraphs.length;
            for (let j = 0; j < p_length; j++) {

                let tokens = paragraphs[j];
                let length = tokens.length;
                for (let i = 0 ; i < length; i++) {
                    let token = tokens[i];

                    let next = i+1;
                    let window = tokens.filter(t=>t.t).slice(next, next + win_size);
                    if(!window.length)
                        window.push(this.vocabulary['<stop>']);
                    while(window.length < win_size) {
                        window.unshift(window[0])
                    }
                    let cnt = length;   //Защита от зацикливания
                    while (cnt-- > 0 && window.length < size) {
                        const idx = Math.floor(Math.random() * this.tokens.length);
                        const t = this.tokens[idx];
                        if (t !== token && !t.system && !window.includes(t))
                            window.push(t);
                    }
                    while(window.length < size) {
                        window.push(this.vocabulary['<stop>']);
                    }
                    let window_cnt = window.map(i=>i.cnt);
                    window_cnt = torus.stack(window_cnt);
                    let mul = torus.einsum(`l,dl->d`, [token.emb, window_cnt]);
                    let sigm = mul.sigm();
                    let res = sigm.MSE(this.TARGET);
                    token.error = res.data[0];
                    res.back();
                    if (Date.now() - time < 5000)
                        continue;

                }
                // this.progress = Math.round(i / length);
                await this.onProgress(Math.round(j / p_length * 100));
                time = Date.now();
            }
        }
        finally {
            this.fire('progress', 0);            
            this['#tokens_error'] = undefined;
            this.losses.push([this.tokens_error]);
            this.progress = 0;
        }
        return tokens;
    }
    get logit_tokens(){
        return this._logit_tokens ??= this.tokens.filter(t => t.id !== undefined).sort((a,b)=>{
            return a.id<b.id?-1:1;
        })
    }
    get tokens(){
        return this._tokens ??= Object.values(this.vocabulary);
    }
    get size(){
        return this.tokens.length;
    }
    tokenize(text, train = false){
        let max = this.char_step;
        let reg = new RegExp(`.{1,${max}}`, 'gs');
        let words = text.match(/[а-яА-Яa-zA-ZЁё]+|./gs);    // Разбили текст на слова и разделители
        words = words.map(w=>{        // Разбили слова на группы букв
            if(w.length < max + 2)
                return w;
            return w.match(reg).map((t, i, items) => {
                if(i>0)
                    t = '_' + t;
                if(items.length > 1 && i < items.length - 1)
                    t += '_'
                return t;
            });
        });
        words = words.flat();

        return words.map(w =>{
            let token = this.add_token(w);
            return (token.emb.token = token.emb.freezed.token = token);
        })
    }
    add_token(word, prev){
        let token = this.vocabulary[word] ??= ((t) => {
            this._tokens = undefined;
            this._logit_tokens = undefined;

            if (!t.endsWith('_')){
                let delta = this.logit_tokens.length - this.logit.shape[0];
                if (delta > 0){
                    this.logit = this.logit.expand(0, 100);
                }
            }


            let lex = /[а-яА-Яa-zA-ZЁё]+/.test(t) || undefined;
            let id = t.length === 1 || !t.endsWith('_')?(this.logit_tokens.last.id + 1): undefined;
            return {
                t,
                lex,
                id,
                stat: [],
                emb: torus.param(torus.rand(this.dim, lex?.1:1))._label('emb: ' + t),
                cnt: torus.param(torus.rand(this.dim))._label('cnt: ' + t),
            }
        })(word)
        if (prev?.t.endsWith('_')){
            prev.stat.add(word);
            prev.logit ??= torus.param(torus.zeros([prev.stat.length, this.dim]).float());
            let delta = prev.stat.length - prev.logit.shape[0];
            if (delta > 0){
                prev.logit = prev.logit.expand(0, delta)
            }
        }

        return token;
    }
}