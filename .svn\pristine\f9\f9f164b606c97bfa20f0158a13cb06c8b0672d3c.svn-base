interface odantForm extends odantComponent, odaAppLayout { }
class odantForm {
    titleLabel = '';
    saveIcon = 'icons:save';
    savingIcon = 'spinners:wind-toy';
    allowCompact = false;
    viewProps: any = {};
    autoSave: number;

    animated = true;
    allowButtons: boolean;
    hideViews: boolean;
    allowClose: boolean;

    settingsId = '';

    dialog = false;

    form: odantForm = this;

    focusedItem: odaItem = null;
    focusedClass: odaClass = null;
    isChanged = false;

    viewId?: string;
    view: odaView = null;
    control: odaControl = null;
    previousView: odaItem = null;


    errors: [any]

    saving = false;
    closing = false;
    _closeDialog = false;

    $saveKey: string;

    needConfirmClose: boolean;


    resolve: Function;
    reject: Function;

    hashchange(): void;
    save(): Promise<void>;
    show(...args: any[]): Promise<any>;

    _close(): Promise;
    _showView($item: odaItem, viewId: string, float: boolean, dialog: boolean): Promise<void>;
    _pushErrors(err: Error): void;
    _onKeyDown(e: KeyboardEvent): void;
}