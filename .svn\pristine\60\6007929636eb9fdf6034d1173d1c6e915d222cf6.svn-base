import {torus} from "../../torus.js";
import {Linear, nn} from "../neuro-module.js";
import {Parser} from './parser.js';
export class Tokenizer extends nn.NeuroModule{
    stats = {};
    constructor(dim = 16, char_step = 3, win_size = 10, negative_size = 5) {
        super(...arguments);
    }
    get targetSize(){
        return this._targetSize ??= this.win_size * (this.negative_size + 1);
    }
    get TARGET(){
        return this._TARGET ??= (()=>{
            const _bins =  Array(this.win_size).fill(1).map((v, i)=>(2. ** - (i + 1) + .5));
            while (_bins.length < this.targetSize)
                _bins.push(0);
            return torus.from(_bins);
        })();
    }
    __init__(){
        this.vocabulary = {
            "<end>": {w: "<end>", end: true,
                emb: torus.zeros(this.dim)._label('emb: <end>'),
                cnt: torus.rand_init(this.dim)._label('cnt: <end>')
            }
        }
    }
    predict(token, predicate_data){
        let stat = token.stat;
        let word = token.w;
        let stats = this.stats[word] ??= (()=>{
            let stats = [];
            let summary = 0;
            for (let w in stat){
                let token = this.vocabulary[w];
                let count = stat[w];
                stats.push({token, count})
                summary += count ** 2;
            }
            summary = Math.sqrt(summary);
            stats.forEach(stat=>{
                stat.count /= summary;
            })
            return stats;
        })()
        let found = stats.map(stat =>{
            let t = stat.token;
            return {
                t, s: torus.cos_similar(t.emb.data, predicate_data) /** stat.count*/
            }
        })
        if (!found.length){
            found = this.tokens.map(t =>{
                return {
                    t, s: torus.cos_similar(t.emb.data, predicate_data)
                }
            })
        }
        found = found.sort((a,b) => a.s>b.s?-1:1);
        return found[0]?.t;
    }
    decode(tokens, word){
        if (word){
            let before = this.tokenize(word);
            tokens.unshift(...before.map(t => t.emb.data));
            word = '';
        }
        return tokens.map(data =>{
            if (word){
                let stat = this.vocabulary[word]?.stat;
                if (stat){
                    let stats = this.stats[word] ??= (()=>{
                        let stats = [];
                        let summary = 0;
                        for (let w in stat){
                            let token = this.vocabulary[w];
                            let count = stat[w];
                            stats.push({token, count})
                            summary += count ** 2;
                        }
                        summary = Math.sqrt(summary);
                        stats.forEach(stat=>{
                            stat.count /= summary;
                        })
                        return stats;
                    })()
                    let found = stats.map(stat =>{
                        let t = stat.token;
                        return {
                            t, s: torus.cos_similar(t.emb.data, data) /** stat.count*/, w: t.w
                        }
                    })
                    found = found.sort((a,b) => a.s>b.s?-1:1);
                    word = found[0]?.w;    // todo multinomial
                    word = word.replace('##', '')
                    return word;
                }
            }
            let found = this.tokens.map(t =>({
                    t, s: torus.cos_similar(t.emb.data, data), w: t.w
                }))
            found = found.sort((a,b) => a.s>b.s?-1:1);
            word = found[0]?.w;    // todo multinomial
            return word;
        }).join('')
    }
    get tokens_error(){
        const size = this.size;
        return this['#tokens_error'] ??= (()=>{
            const tokens = this.tokens.filter(i=>(i.error>0 && i.error<1))

            if (!size)
                return 1;
            let error = tokens.reduce((r, t) =>{
                return r + t.error;
            }, 0)
            error /= size;
            return  error;
        })()
    }
    get error(){
        return this.tokens.filter((_,i)=>i).map(i=>i.error).avg();
    }
    set progress(n){
        this.onProgress(n)
    }
    async onProgress(progress){

    }
    async train(text){
        let win_size = this.win_size;
        let size = this.targetSize;
        let tokens = this.tokenize(text, true);
        let length = tokens.length;
        this.progress = 0;
        try{
            let time = Date.now();
            for (let i = 0 ; i< length; i++) {
                let token = tokens[i];

                let next = i+1;
                let window = tokens.filter(t=>t.w).slice(next, next + win_size);
                if(!window.length)
                    window.push(this.vocabulary['<end>']);
                while(window.length < win_size) {
                    window.unshift(window[0])
                }
                let cnt = length;   //Защита от зацикливания
                while (cnt-- > 0 && window.length < size) {
                    const idx = Math.floor(Math.random() * this.tokens.length);
                    const t = this.tokens[idx];
                    if (t !== token && !t.system && !window.includes(t))
                        window.push(t);
                }
                while(window.length < size) {
                    window.push(this.vocabulary['<end>']);
                }
                let window_cnt = window.map(i=>i.cnt);
                window_cnt = torus.stack(window_cnt);
                let mul = torus.einsum(`l,dl->d`, [token.emb, window_cnt]);
                let sigm = mul.sigm();
                let res = sigm.MSE(this.TARGET);
                token.error = res.data[0];
                res.back();
                if (Date.now() - time < 1000)
                    continue;

                // this.progress = Math.round(i / length);
                await this.onProgress(Math.round(i / length * 100));
                time = Date.now();
            }
        }
        finally {
            this.fire('progress', 0);            
            this['#tokens_error'] = undefined;
            this.losses.push([this.tokens_error]);
            this.progress = 0;
        }
        return tokens;
    }
    get tokens(){
        return this._tokens ??= Object.values(this.vocabulary);
    }
    get size(){
        return this.tokens.length;
    }
    tokenize(text, train = false){
        let words = text.match(/[а-яА-Яa-zA-ZЁё]+|[^а-яА-Яa-zA-ZЁё]+/gs);
        let reg = /[а-яА-Яa-zA-ZЁё]/;
        let max = this.char_step;
        let voc = {};
        for (let word of words){
            for (let i = 0; i < word.length; i++){
                let t = word[i];
                if (i && reg.test(t))
                    t = '##' + t;
                for (let j = 1; j <= max; j++){
                    voc[t] = ~~voc[t] + 1;
                    t += word[i + j] || '';
                }
            }
        }
        voc = Object.entries(voc);
        // Берём все односимвольные токины с учётом префикса ##, токины максимально длины из начала слова, часто встречающиеся токины
        let b_reg = new RegExp(`^[а-яА-Яa-zA-ZЁё]{${max}}`);
        voc = voc.filter(v=> v[0].length === 1 || /^##.$/s.test(v[0]) || b_reg.test(v[0]) || v[1] > 1);
        voc = voc.map(i=>i[0]);
        let voc2 = [];
        for (let word of words){
            let i = 0;
            while (i < word.length){
                let j = max;
                while ( j ){
                    let t = word.substr(i, j--) || '';
                    if (i && reg.test(t))
                        t = '##' + t;
                    if (voc.includes(t)){
                        voc2.push(t);
                        break;
                    }
                }
                i += j + 1;
            }
        }
        let tokens = [];
        let token = null;
        for (let w of voc2) {
            token = this.add_token(w, train?token:null);
            token.emb.token = token.emb.freezed.token = token;
            tokens.push(token);
        }
        return tokens;
    }
    add_token(word, prev){
        let token = this.vocabulary[word] ??= ((w) => {
            this._tokens = undefined;
            return {
                stat: [],
                w,
                emb: torus.param(torus.rand_init(this.dim))._label('emb: ' + w),
                cnt: torus.param(torus.rand_init(this.dim))._label('cnt: ' + w),
                // net: new TokenLayer(this.dim, 2)
            }
        })(word)
        if (word[0] !== '#'){
            token.start = true;
        }
        else if (prev && !prev.stat.includes(word)){
            prev.stat.push(word);
            //todo добавление выхода в логит
        }
        return token;
    }
}
export class TokenLayer extends nn.Module{
    constructor(dim, expand = 2){
        super(...arguments)
    }
    __init__(){
        this.d_inner = this.expand * this.dim;
        this.layer1 = new nn.Linear(this.d_inner, this.d_inner);
        this.layer2 = new nn.Linear(this.d_inner, this.dim);
    }
    forward(x){
        let y = this.layer1(x);
        y = y.relu();
        y = this.layer2(y);
        return y;
    }
}