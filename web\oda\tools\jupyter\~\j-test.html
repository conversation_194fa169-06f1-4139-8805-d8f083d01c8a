<oda-ttt></oda-ttt>

<script type="module">

    import '../../oda.js';

    ODA({
        is: 'oda-ttt', template: `
        <iframe :srcdoc style="width: 100%; height: 100vh"></iframe>
    `,
    get srcdoc() {
        return `
        <script>
            window.addEventListener("message", (e) => {
                let root = document.body.children[0];
                console.log(e.data)
                for (let key in e.data) {
                    root.setAttribute(key, e.data[key]);
                }
            }, false)
        <\/script>
        <progress value=".5"></progress>
    `
    },
    set value(n) {
        this.$('iframe').contentWindow.postMessage({value: n})
    }
})

</script>