{"cells": [{"cell_type": "text", "source": ["1234567890"], "metadata": {"id": "text-0"}, "state": "error", "time": ""}, {"cell_type": "code", "source": ["data = 1234567890\nsetTimeout(() => {\n   fetch('./preset.css').then(res => {\n        res.text().then(txt => {\n            data = txt\n            runNext()\n       })\n   })\n}, 2000)\n>data"], "metadata": {"id": "code-0"}}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-1"}}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-2"}, "time": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-3"}, "time": ""}]}