import '../oda/rocks.js';
import { attachData } from './data.js';
const SERVER_MODE = typeof globalThis.process !== 'undefined';
const SSID = globalThis.SSID;
const DEFAULTORIGIN = globalThis.DEFAULTORIGIN /* || 'https://current.odant.org' */;
const SIDE = SERVER_MODE ? 'server' : 'client';

if (!globalThis.CORE) {
    // заглушки для сервера
    ['ODA', 'ODANT', 'FIELD_CONTROL', 'FIELD_TABLE', 'FIELD_INPUT', 'VIEW', 'FIELD_TOOLBAR']
        .forEach(key => (globalThis[key] = (() => { })));

    globalThis.CORE = ROCKS;
    globalThis.CORE.deferredFields ??= {};
    //#region additional functions

    function getBool(v, def = false) {
        if (!v)
            return def;
        switch (typeof v) {
            case 'object': return true;
            case 'string': return v.toLowerCase() === 'true';
            case 'boolean': return v;
            case 'number': return v !== 0;
        }
        return false;
    }
    const prefixMap = {
        H: 'host',
        P: 'part',
        B: 'base',
        // S: 'system',
        W: 'workplace',
        D: 'domain',
        M: 'module',
        C: 'class',
        O: 'object',
        // I: 'index',
    }

    globalThis.RequestError = globalThis.RequestError || class RequestError extends Error {
        constructor(response, message) {
            super(message);
            this.errorCode = response.headers.get('odant-error-code');
            this.status = response.status;
            this.statusText = response.statusText;
            this.url = response.url;
        }
    };
    globalThis.WebSocketEvents = globalThis.WebSocketEvents || class WebSocketEvents {
        constructor(url) {
            this._url = url;
            this._start();
        }
        _start() {
            try {
                this._webSocket = new WebSocket(this._url);
                this._webSocket.onopen = (event) => this._onOpen(event);
                this._webSocket.onclose = (event) => this._onClose(event);
                this._webSocket.onmessage = (event) => this._onMessage(event);
                this._webSocket.onerror = (event) => this._onError(event);
            } catch (e) { }
        }
        _onOpen(event) {
            this._opened = true;
            this._recoverySubscribe();
            console.log('Open webSocket connection');
        }
        _onClose(event) {
            if (!SERVER_MODE) {
                setTimeout(() => {
                    this._start();
                }, 3000);
                if (this._opened)
                    console.log('Close webSocket connection');
            }
            this._opened = false;
        }
        _onError(event) {
            if (this._opened)
                console.log('Error in webSocket connection', event);
            this._webSocket.close();
        }
        _onMessage(event) {
            let info = JSON.parse(event.data);
            if (info.event && (info.data || info.custom))
                this._fire(info.event, (info.data || info.custom));
        }
        _fire(name, data) {
            this._callbacks = this._callbacks || {};
            let args = [].slice.call(arguments, 1);
            let callbacks = this._callbacks[name];

            if (callbacks) {
                callbacks = callbacks.slice(0);
                for (let i = 0, len = callbacks.length; i < len; ++i) {
                    callbacks[i].apply(this, args);
                }
            }

            return this;
        }
        _is_connected() {
            return this._webSocket && this._webSocket.readyState === 1;
        }
        _send(data) {
            if (this._is_connected() && (typeof data === 'string') && (data.length > 0)) {
                this._webSocket.send(encodeURIComponent(data));
            }
        }
        _sendSubscribe(name) {
            if (this._is_connected()) this._send('+' + name);
        }
        _sendUnSubscribe(name) {
            if (this._is_connected()) this._send('-' + name);
        }
        _sendUnSubscribeAll() {
            if (this._is_connected()) this._send('-*');
        }
        _recoverySubscribe() {
            if (this._is_connected() && this._callbacks) {
                let data = '';
                for (let name in this._callbacks) {
                    data += '+' + name + '\n';
                }
                if (data.length > 0) this._send(data);
            }
        }
        _isNotEmptyString(str) {
            return (typeof str === "string") && str.length > 0;
        }
        _constructCustomName(name) {
            return 'custom:' + name;
        }
        _constructUserMessageName() {
            return 'user-message';
        }
        addEventListener(name, callback) {
            if (!this._isNotEmptyString(name)) return this;
            this._callbacks = this._callbacks || {};
            this._callbacks[name] = this._callbacks[name] || []
            if (this._callbacks[name].push(callback) === 1)
                this._sendSubscribe(name);
            return this;
        }
        removeEventListener(name, callback) {
            this._callbacks = this._callbacks || {};

            if (0 === arguments.length) return this;
            if (!this._isNotEmptyString(name)) return this;

            // specific event
            let callbacks = this._callbacks[name];
            if (!callbacks) return this;

            // remove all handlers
            if (1 === arguments.length) {
                this._sendUnSubscribe(name);
                delete this._callbacks[name];
                return this;
            }

            // remove specific handler
            let cb;
            for (let i = 0; i < callbacks.length; i++) {
                cb = callbacks[i];
                if (cb === callback) {
                    callbacks.splice(i, 1);
                    break;
                }
            }

            if (0 === callbacks.length)
                this._sendUnSubscribe(name);

            return this;
        }
        addCustomListener(name, callback) {
            if (!this._isNotEmptyString(name)) return this;
            return this.addEventListener(this._constructCustomName(name), callback);
        }
        removeCustomListener(name, callback) {
            if (!this._isNotEmptyString(name)) return this;
            return this.removeEventListener(this._constructCustomName(name), callback);
        }
        addUserMessageListener(callback) {
            return this.addEventListener(this._constructUserMessageName(), callback);
        }
        removeUserMessageListener(callback) {
            return this.removeEventListener(this._constructUserMessageName(), callback);
        }
        removeAllListeners() {
            this._callbacks = {};
            this._sendUnSubscribeAll();
            return this;
        }
        listeners(name) {
            this._callbacks = this._callbacks || {};
            return this._callbacks[name] || [];
        }
        hasListeners(name) {
            return !!this.listeners(name).length;
        }
        get URL() {
            return this._url;
        }
        sendCustomEvent(name, eventObject) {
            if (!this._isNotEmptyString(name)) {
                throw Error("sendCustomEvent error: name is not string or empty");
            }
            let isDataString = (typeof eventObject === "string");
            let isDataObject = (typeof eventObject === "object");
            if (!isDataString && !isDataObject) {
                throw Error("sendCustomEvent error: eventObject must be string or object");
            }
            const msgObj = {
                root: {
                    event: this._constructCustomName(name),
                    custom: JSON.stringify(eventObject)
                }
            };
            this._send(JSON.stringify(msgObj));
        }
        sendUserMessage(userId, message) {
            if (!this._isNotEmptyString(userId)) {
                throw Error("sendUserMessage error: userId is not string or empty");
            }
            let isDataString = (typeof message === "string");
            let isDataObject = (typeof message === "object");
            if (!isDataString && !isDataObject) {
                throw Error("sendUserMessage error: message must be string or object");
            }
            if (this._is_connected()) this._send('&' + userId + ':' + JSON.stringify(message));
        }
        close() {
            if (this._webSocket) this._webSocket.close();
        }
    };
    CORE.fetch = function (href = '/', method = '', params = {}, postObject) {
        let url = encodeURI(href + (method ? '?' + method : ''));
        url += Object.keys(params).reduce((res, p) => {
            res += ((res || method) ? '&' : '?') + encodeURIComponent(p) + '=' + encodeURIComponent(params[p]);
            return res;
        }, '');
        if (SERVER_MODE && SSID) url += `${url.includes('?') ? '&' : '?'}ssid=${SSID}`;
        if (postObject && typeof postObject !== 'undefined' && typeof postObject !== 'string' && postObject.constructor.name !== FormData.prototype.constructor.name) {
            postObject = JSON.stringify(postObject, (k, v) => k.startsWith('__') ? undefined : v);
        }
        params = { method: postObject ? 'POST' : 'GET', body: postObject, credentials: 'include' };
        if (postObject) { //todo пока оставим
            params['headers'] ??= { "Cache-Control": 'must-understand, no-store' };
        }
        return fetch(url, params);
    }
    CORE.request = async function request(href = '/', method = '', params = {}, postObject) {
        try {
            const response = await CORE.fetch(href, method, params, postObject);
            switch (response.status) {
                case 200: {
                    let content_type_header = response.headers.get('Content-Type');
                    let res = content_type_header ? content_type_header.split(';')[0] : content_type_header;
                    switch (res) {
                        case 'application/x.odant.async':
                        case 'application/x.odant.async+json':
                        case 'text/x-json':
                        case 'application/json':
                            return response.json();
                        case 'text/cmd':
                        case 'text/css':
                        case 'text/csv':
                        case 'text/javascript':
                        case 'application/javascript':
                        case 'text/php':
                        case 'text/html':
                        case 'text/plain':
                        case 'text/xml':
                            return response.text();
                        case 'image/png':
                        case 'image/gif':
                        case 'image/jpg':
                        case 'image/jpeg':
                        case 'application/octet-stream':
                        default: {
                            try {
                                return await response.blob();
                            }
                            catch (err) {
                                throw new RequestError(response, 'Unresolved fetch mime-type: ' + res);
                            }
                        }
                        // default: {
                        //     throw new RequestError(response, 'Unresolved fetch mime-type: ' + res);
                        // }
                    }
                }
                case 500: {
                    const text = await response.text();
                    throw new RequestError(response, text);
                }
                default: {
                    // throw new RequestError(response, response.statusText);
                }
            }
        }
        finally {

        }
    }
    CORE.getContextFromUrl = function (pathname) {
        pathname = decodeURI(pathname);
        let start = pathname.startsWith('/api/') ? 4 : 0;
        let end = pathname.lastIndexOf('/~');
        end = end > -1 ? end : pathname.length;
        return pathname.substring(start, end);
    }

    if (!SERVER_MODE && document.location.host /* && !document.location.host.startsWith('localhost') */)
        CORE.URL = new URL(document.location.origin);
    else
        CORE.URL = new URL(DEFAULTORIGIN);
    //#endregion additional functions

    //#region odaAccesses
    const odaAccesses = ['n', 'p', 'r', 'w', 'c', 'd', 'a']
    globalThis.odaAccess = class odaAccess extends CORE({
        level: undefined,
        get label() {
            switch (this.level) {
                case 6: return 'admin';
                case 5: return 'delete';
                case 4: return 'create';
                case 3: return 'write';
                case 2: return 'read';
                case 1: return 'preview';
                case 0: return 'none';
                default: return 'unknown';
            }
        },
        get color() {
            switch (this.level) {
                case 6: return 'var(--access-admin-color)';
                case 5: return 'var(--access-rwcd-color)';
                case 4: return 'var(--access-rwc-color)';
                case 3: return 'var(--access-rw-color)';
                case 2: return 'var(--access-r-color)';
                case 1: return 'var(--access-p-color)';
            }
            return 'var(--access-none-color)';
        },
        get letter() {
            return this.label[0].toUpperCase();
        },
        get isAdmin() {
            return parseInt(this.level) >= 6;
        },
        allow(access) {
            return (new odaAccess(access)).level <= this.level;
        }
    }) {
        constructor(access = 0, owner) {
            super(...arguments);
            switch (typeof access) {
                case 'number': {
                    this.level = access;
                } break;
                case 'string': {
                    this.level = odaAccesses.indexOf(access.toLowerCase()[0]);
                } break;
                case 'object': {
                    this.level = access.level;
                } break;
            }
            this.$owner = owner;
        }
    }
    //#endregion odaAccesses

    //#region odaInherit
    globalThis.odaInherit = class odaInherit extends CORE({
        name: '',
        get color() {
            switch (this.name) {
                case 'owner': return 'var(--inherit-owner-color)';
                case 'parent': return 'var(--inherit-parent-color)';
                case 'type': return 'var(--inherit-type-color)';
                case 'core': return 'var(--inherit-core-color)';
                case 'link': return 'var(--inherit-link-color)';
                default: return 'var(--inherit-self-color)';
            }
        },
        get isSelf() {
            return !this.name || this.name === 'self';
        },
        get isOwner() {
            return this.name === 'owner';
        },
        get isParent() {
            return this.name === 'parent';
        },
        get isType() {
            return this.name === 'type';
        },
        get isCore() {
            return this.name === 'core';
        },
        get isLink() {
            return this.name === 'link';
        },
        get prefix() {
            return this.name?.[0]?.toUpperCase() || '';
        }
    }) {
        constructor(name) {
            super(...arguments);
            this.name = name;
        }
    }
    //#endregion odaInherit

    //#region odaItem
    globalThis.odaItem = class odaItem extends CORE({
        getItemCtor(type, ext) {
            return this._itemCtors[ext || type] ??= this.$owner?.getItemCtor(type, ext);
        },
        _itemCtors: {},
        getHandlerCtor(type, ext, path) {
            return (this._itemCtors[`${type}${ext || ''}${path}`] ??= this.$owner?.getHandlerCtor(type, ext, path));
        },
        get data() {
            return this.draft;
        },
        get description() {
            return this.typeLabel + `: ${this.label}`;
        },
        isSystem: false,
        $owner: null,
        isChanged: false,
        changeCount: 0,
        category: '',
        // get fullCategory() {
        //     return this.category;
        // },
        get inherit() {
            return new odaInherit(this.draft?.inherit);
        },
        get typeLabel() {
            return this.typeName;
        },
        $public: {
            id: {
                $type: String,
                $readOnly: true
            },
            name: {
                $readOnly: true,
                get() {
                    const data = this.data || this.draft;
                    return data.name || data.Name || data.n || this.id;
                },
            },
            label: {
                $type: String,
                get() {
                    const data = this.data || this.draft;
                    return data.label || data.Label || data.l || this.name;
                },
                set(n) {
                    this.data.label = this.data.Label = n;
                }
            },
            typeName: {
                $type: String,
                $readOnly: true,
                $def: 'item'
            },
            icon: {
                $type: String,
                $def: 'odant:item',
                $editor: '@lib/icon-editor[odant-icon-editor]'
            }
        },
        get path() {
            if (!this.prefix)
                return this.$owner?.path + '/' + this.id;
            let owner = this.$owner;
            while (owner?.prefix === this.prefix) {
                owner = owner.$owner;
            }
            return (owner?.path || '') + '/' + this.prefix + ':' + this.id;
        },
        get displayLabel() {
            return this.typeLabel + ': ' + this.label;
        },
        get fullPath() {
            return (this.$owner?.fullPath || '') + '/' + (this.prefix ? (this.prefix + ':') : '') + this.id;
        },
        get available() {
            return (this.draft.available === false) ? false : true;
        },
        get $user() {
            return this.$host.$user;
        },
        get formUrl() {
            return this.url + `/~/client/pages/${this.defaultForm}/index.html`;
        },
        get $$files() {
            return this.$folder.then(folder => {
                return folder.$$files;
            })
        },
        get $$folders() {
            return this.$folder.then(folder => {
                return folder.$$folders;
            })
        },
        get access() {
            return this.$owner.access;
        },
        get color() {
            return `var(--item-${this.typeName}-color)`;
        },
        get fill() {
            return this.color;
        },
        get order() { return 0 },
        // No $class in host
        get $class() { return this.$owner?.$class; },
        // No $base in host
        get $base() { return this.$owner?.$base; },
        get $host() { return this.$owner.$host; },
        get $domain() { return this.$owner?.$domain; },
        get $module() { return this.$owner?.$module; },
        get $workplace() { return this.$owner?.$workplace; },
        get $part() { return this.$owner?.$part; },
        get $$items() {
            return this.cache('$$items', async () => {
                let items = [];
                for (let i in this) {
                    if (i.startsWith('$$') && i !== '$$items') {
                        const res = await this[i];
                        if (Array.isArray(res))
                            items.push(...res);
                    }
                }
                return items;
            })
        },
        show(float = false) {
            return this.getHandler(this.defaultView).then(view => {
                return view?.execute(float);
            })
        },
        getFile(step) {
            return this.$folder.then(f => {
                return f.getFile(step);
            })
        },
        getFolder(step) {
            return this.$folder.then(f => {
                return f.getFolder(step);
            })
        },
        async getFileItem(path) {
            return (await this.getFile(path)) || (await this.getFolder(path));
        },
        save(body) {
            if (!this.access.allow('w')) return;
            body = body || this.body;
            if (!body) return;
            return this.request(undefined, undefined, body).then(res => {
                this.isChanged = false;
            })
        },
        HANDLERS: {
            $group: 'handlers',
            $$handlers: {
                $type: Array,
                async get() {
                    return new Promise((resolve) => {
                        setTimeout(async () => {
                            let handlers = await this.handlersList;
                            this.__handlersCache ??= Object.create(null);
                            handlers = handlers?.map(async i => {
                                return this.__handlersCache[i.id || i.name] ??= await odaHandler.build(i, this);
                            });
                            if (!handlers) return [];
                            handlers = await Promise.all(handlers);
                            handlers = handlers.filter(i => i?.active);
                            resolve(handlers);
                        });
                    });
                }
            },
            handlersList: {
                $type: Array,
                async get() {
                    const structure = await this.$handlerSettingsStorage?._handlerStructure;
                    let handlers = [];
                    if (!structure) return handlers;
                    if (this instanceof odaHandler) {
                        handlers.push(...structure['handler'] || []);
                    }
                    else {
                        let proto = this;
                        while (proto && (proto = proto.__proto__?.__proto__)) {
                            handlers.push(...structure[proto.typeName] || []);
                        }
                    }
                    return handlers.flatMap(i => i.H)?.reduce((res, h) => {
                        if (res.every(i => i.name !== h.name)) {
                            res.push(h);
                        }
                        return res;
                    }, []);
                }
            },
            get $handlerSettingsStorage() {
                let storage = this;
                while (storage && !(storage instanceof odaStructureItem)) {
                    storage = storage.$owner;
                }
                return storage;
            },
            async getHandler(path) {
                let $handler;

                // $handler = this.__handlersCache?.[path];
                // if ($handler?.active) {
                //     return $handler;
                // }
                // else {
                    const names = path.split('/');
                    for (const name of names) {
                        if (!$handler) {
                            const handlers = await this.handlersList;
                            const draft = handlers.find(h => h.id === name || h.name === name);
                            if (!draft) return;
                            this.__handlersCache ??= Object.create(null);
                            $handler = this.__handlersCache[draft.id || draft.name] ??= await odaHandler.build(draft, this);
                        }
                        else {
                            const $$childHandlers = await $handler.$$childHandlers;
                            $handler = $$childHandlers.find(h => h.name === name);
                        }

                        if (!$handler?.active)
                            return undefined;
                    }
                // }
                return $handler;
            },
            async executeHandler(name, params, content) {
                const handler = await this.getHandler(name);
                if (!handler) {
                    console.error(`Handler "${name}" not found for "${this.label} [${this.path}]"`)
                    return;
                }

                if (SERVER_MODE) {
                    CORE.__running_handlers.add(handler);
                    try {
                        return await handler.execute(params, content);
                    }
                    catch (err) {
                        console.error(`ERROR on execute handler "${handler.name}" in ${this.typeName} "${this.label}"`, err);
                    }
                    finally {
                        CORE.__running_handlers.delete(handler);
                    }
                }
                else {
                    return handler.execute(params, content);
                }
            },
            async registerTask(name, params) {
                if (!this.body) await this.load();
                const handler = await this.getHandler(name);
                if (!handler.schedule) throw new Error(`Empty schedule in ${this.typeName} "${this.path}", handler "${handler.name}"`);
                if (handler && handler.active) {
                    console.log('handler.schedule', handler.schedule);
                    const tasks = handler.schedule.split('|');
                    //yyyy-MM-ddTHH:00:ss
                    let now = new Date();
                    const dates = tasks.map(task => {
                        try {
                            let target;
                            if (/[yMdHms]/.test(task)) {
                                const m2daTask = m2da(task);
                                const d2daNow = d2da(now);
                                const next_ex = nextExec(m2daTask, d2daNow)
                                if (next_ex[0] === 'passed') return Number.MAX_SAFE_INTEGER;
                                else target = da2d(next_ex)
                            } else {
                                target = new Date(task);
                            }
                            return target;
                        } catch (err) {
                            console.log(err);
                            return Number.MAX_SAFE_INTEGER;
                        }
                    }).sort();

                    const target = dates[0];
                    if (target) {
                        try {
                            const ms = target.valueOf() - now.valueOf();
                            if (Number.isNaN(ms)) throw new Error(`Bad target dateTime ${this.typeName} "${this.path}", handler "${handler.name}"`);
                            if (ms <= 0) throw new Error(`Bad task mask in ${this.typeName} "${this.path}", handler "${handler.name}"`);
                            setTimeout(async () => {
                                try {
                                    console.log('execute on-time', ms);
                                    await handler.execute(params);
                                } catch (err) {
                                    console.error(err);
                                } finally {
                                    setTimeout(() => {
                                        this.registerTask(name, params);
                                    }, 1000);
                                }
                            }, ms);
                        } catch (err) {
                            console.error(err);
                        }
                    }
                    return;
                }
                function m2da(m) {
                    const [date, time] = m.split('T');
                    return [...date.split('-'), ...time.split(':')].map(s => {
                        const res = parseInt(s);
                        return Number.isNaN(res) ? '*' : res;
                    });
                }
                function d2da(d) { return [d.getFullYear(), d.getMonth(), d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds()] }
                function da2d(da) {
                    let d = new Date()
                    d.setFullYear(da[0], da[1], da[2])
                    d.setHours(da[3], da[4], da[5], 0)
                    return d
                }

                function minDT(dat, i) {
                    let minDt = [0, 0, 1, 0, 0, 0]
                    return minDt[i]
                }

                function dopDT(dat, i) {
                    let dopDt = [undefined, 12, NaN, 24, 60, 60]
                    if (i !== 2) return dopDt[i]
                    else {
                        let nDat = new Date(dat.setDate(1))
                        nDat = new Date(dat.setHours(0, 0, 0, 0) - 1)
                        return nDat.getDate()
                    }
                }

                function maxDT(dat, i) {
                    let maxDt = [undefined, 11, NaN, 23, 59, 59]
                    if (i !== 2) return maxDt[i]
                    else {
                        let nDat = new Date(dat.setDate(1))
                        nDat = new Date(dat.setHours(0, 0, 0, 0) + 35 * 24 * 60 * 60 * 1000)
                        return dopDT(nDat, i)
                    }
                }

                function calcDat(da, i, val) {
                    let rez = da
                    if (i === 0) { rez[i] += val; return rez }
                    else {
                        let d = da2d(da)
                        if ((da[i] + val) > maxDT(d, i)) { rez[i - 1]++; return calcDat(rez, i, val - maxDT(d, i)) }
                        if ((da[i] + val) < minDT(d, i)) { rez[i - 1]--; return calcDat(rez, i, val + dopDT(d, i)) }
                        else { rez[i] += val; return rez }
                    }
                }

                function nextExec(ma, da = d2da(new Date())) {
                    let ambit = [], sign = 0, l = da.length, rez = da
                    for (let i = 0; i < l; i++) {
                        (typeof ma[i] !== 'string') ? ambit[i] = ma[i] - da[i] : ambit[i] = ma[i]
                    }
                    for (let i = l - 1; i >= 0; i--) {
                        if (ambit[i] < 0) sign = -i
                        if (ambit[i] > 0) sign = 1
                    }
                    if (sign < 0) {
                        for (let i = -sign; i >= 0; i--) {
                            if (typeof ambit[i] == 'string') { ambit[i] = 1; sign = 1; break }
                        }
                    }
                    if (sign === 0) { return ['passed'] }

                    ambit.forEach((e, i) => {
                        if (typeof e == 'number') { rez = calcDat(rez, i, e) }
                    })
                    return rez
                }
                throw new Error(`Handler "${name}" not found in ${this.typeName} "${this.label}"`);
            },
        },

        async import(path) {
            return this.cache(`import:${path}`, async () => {
                const file = await this.getFile(path);
                let url = file?.draft?.path;
                if (url) {
                    if (path.includes('~/') && url.startsWith('/web')) {
                        url = '/api' + url;
                    }
                }
                else {
                    url = this.url + '/' + path
                }
                return import(url);
            });
        },

        cache(key, callback) {
            let result = this.__customCache__[key];
            if (result === undefined) {

                switch (callback.constructor.name) {
                    case 'AsyncFunction': {
                        let resolve;
                        result = this.__customCache__[key] = new Promise(res => { resolve = res; });
                        resolve(callback());
                    } break;
                    case 'Function': {
                        result = this.__customCache__[key] = callback();
                    } break;

                    default: {
                        result = this.__customCache__[key] = callback;
                    } break;
                }
            }
            return result;
        },
        resetCache(key) {
            if (key) this.__customCache__[key] = undefined;
            else this.__customCache__ = {};
        },
        getFormUrl(form, view, context) {
            const $form = `${form || this.defaultForm}/index.html`;
            const $view = view ? `#view=${view}` : '';
            const $context = context ? `#/${context}` : '';
            const url = `${this.url}/~/client/pages/${`${$form}${$context}${$view}`.replace(/\/\//g, '/')}`;

            return url;
        },
        async open(page = 'form', view, context) {
            if (SERVER_MODE) return;
            window.open(this.getFormUrl(page, view, context), '_blank');
        },
        navigate(form = this.defaultPage, view) {
            if (SERVER_MODE) return;
            const url = this.getFormUrl(form, view);
            window.open(url);
        },
        getRelativePath(item) {
            if (!item || !item.path) {
                return null;
            }

            let res = item.path;
            let cnt_id = this.path;
            while (cnt_id.indexOf('/') > -1) {
                res = res.replace(cnt_id + '/', '');
                cnt_id = cnt_id.substring(0, cnt_id.lastIndexOf('/'));
            }
            return res;
        },
    }) {
        // body = null;
        itemCache = {};
        constructor(draft, owner) {
            const id = draft.id;
            super(...arguments);
            this['#id'] = id;
            this._$extends = draft?.$extends;
            this.draft = draft;
            this.$owner = owner;
            this.listen('changed', () => {
                if (this.body && !this.isChanged)
                    this.load?.();
            })
        }
        toJSON() {
            const props = Object.values(this.constructor.__rocks__.descrs).filter(i => i.enumerable).filter(i => {
                return i.$type;
            });
            return props.reduce((res, i) => {
                const val = this[i.name];
                if (!(val instanceof Object))
                    res[i.name] = this[i.name];
                return res;
            }, {})
        }
        static create(draft = {}, owner) {
            const id = draft.id || draft.i || draft.oid || draft.name || draft.Name;
            const cache = owner.itemCache[this.prototype.typeName] ??= Object.create(null);
            const item = (cache[id] ??= new this(draft, owner));
            item._$extends = draft?.$extends;
            item.draft = draft;
            return item
        }
        static destroy($item) {
            const id = $item.id;
            const cache = $item.$owner.itemCache[$item.constructor.prototype.typeName];
            if (!cache) return;
            delete cache[id];
        }
        static build(draft = {}, owner) { //odaItem
            const cache = owner.itemCache[this.prototype.typeName] ??= Object.create(null);
            const id = draft.id || draft.i || draft.oid || draft.name || draft.Name;
            return cache[id] ??= owner.getItemCtor(this.prototype.typeName).then(async ctor => {
                let item = new ctor(draft, owner);
                if (item instanceof odaStructureItem) {
                    const myCtor = await item.getItemCtor(this.prototype.typeName);
                    if (ctor !== myCtor)
                        item = new myCtor(draft, owner);
                }
                return item;
            })
        }
        static defaultView = '';
        static defaultPage = '';
        static defaultForm = '';
        getDefHandler(type) {
            return this.data?.[`default-${type.toKebabCase()}`] || this.constructor[`default${type.toCamelCase().toCapitalCase()}`];
        }
        setDefHandler(type, value) {
            const key = `default-${type.toKebabCase()}`;
            const def = this.getDefHandler(type);

            if (value === def)
                value = '';

            if (!this.data[key] && !value)
                return;

            this.data[key] = value;
        }
        __customCache__ = {}
    }
    //#endregion odaItem

    //#region odaVirtualItem
    globalThis.odaVirtualItem = class odaVirtualItem extends odaItem.ROCKS({
        get data() {
            return this.draft;
        },
    }) { }
    //#endregion odaVirtualItem

    FIELDS: {
        //#region odaFindField
        globalThis.odaFindField = class odaFindField extends odaItem.ROCKS({
            async getField(path) {
                if (!path) return;
                if (path.startsWith('/')) {
                    path = path.slice(1);
                }
                const steps = path.split('/');
                const step = steps.shift().replace('@', '');
                const fields = await this.$$fields;
                let field = fields.find(i => i.name === step);
                if (!field) {
                    field = this.$FIELDS;
                    if (field) {
                        field = await field.getField(path);
                    }
                }
                else if (steps.length) {
                    field = await field.getField(steps.join('/'));
                }
                return field;
            },
            async findFieldByPath(path) {
                const items = await this.$$fields;
                let res;
                for (const item of items) {
                    if (item.path === path) {
                        res = item;
                    }
                    else {
                        res = item.findFieldByPath(path);
                    }
                    if (res) return res;
                }
                return null;
            }
        }) {}
        //#region odaFieldOwner
        globalThis.odaFieldOwner = class odaFieldOwner extends odaItem.ROCKS({extends: [odaFindField],
            get METADATA() {
                return this.data?.$METADATA?.[0];
            },
            get hasFields() {
                return this.$FIELDS.hasFields;
            },
            get $FIELDS() {
                return this.cache('$FIELDS', () => new odaExtFields(this.data.$METADATA[0], this));
            },
            get $STATIC() {
                return this.cache('$STATIC',() => new odaStaticFields(this.data.$STATIC[0], this));
            },
            get $DETAILS() {
                return this.$base?.$STATIC;
            },
            get $$fields() { //todo this.$FIELDS.$$fields
                if (this.$DETAILS)
                    return [this.$FIELDS, this.$STATIC, this.$DETAILS];
                return [this.$FIELDS, this.$STATIC];
            },
        }) {}
        //#endregion odaFieldOwner

        //#region odaField
        globalThis.odaField = class odaField extends odaVirtualItem.ROCKS({extends: [odaFindField],
            $typeClass: Object,
            $handlerSettingsStorage: {
                $type: Object,
                get() {
                    return this.$typeClass || this.$storage || this.$base // для odaDetailsFields;
                }
            },
            tag: 'ATTR',
            typeName: 'field',
            id: {
                $type: String,
                get() {
                    return this.name;
                }
            },
            name: {
                $type: String,
                get() {
                    return this.data.Name || this.data.name;
                }
            },
            virtual: {
                get() {
                    return this.data?.virtual ?? this.$owner.virtual;
                },
            },
            hide: {
                $type: Boolean,
                get() {
                    return this.data.hide || this.data.Hide;
                },
                set(n) {
                    this.data.hide = n;
                    if ('Hide' in this.data) {
                        this.data.Hide = n;
                    }
                }
            },
            isSystemField: {
                get() {
                    return this.data.system;
                }
            },
            $public: {
                type: {
                    // $type: String,
                    $editor: '@lib/type-editor[odant-type-editor]',
                    $description: 'This is type of field (any odaClass)',
                    get() {
                        return this.data.type || this.data.Type;
                    },
                    async set(v) {
                        // const idx = this.$owner.$$fields.indexOf(this);
                        this.constructor.destroy(this);
                        if (v) {
                            if (v instanceof odaItem) {
                                v = this.$storage.getRelativePath(v);
                                (this.$owner.$base || this.$owner.$part || this.$owner).findItem(v, 'all').then(c => (this.$typeClass = c));
                            }
                        }
                        else
                            this.$typeClass = null;
                        this.data.type = v;
                        if (this.data.Type)
                            this.data.Type = v;
                        this.$owner.$$fields = undefined;
                        this.async(() => {
                            this.$owner.reset();
                        }, 100);
                    }
                },
                isTable: {
                    $type: Boolean,
                    get() {
                        return this.data?.list || this.data?.List;
                    },
                    set(v) {
                        this.data.list = v;
                        if (this.data.List) this.data.List = v;
                        this.$owner.reset();
                    }
                },
                defaultView: {
                    $group: 'editors',
                    $editor: '@lib/field-control-editor[odant-field-control-editor]',
                    $def: null,
                    get $list() {
                        return this?.$typeClass?._fieldViews[this.defaultViewAttr] || [];
                    },
                    get() {
                        let view = this.data[this.defaultViewAttr];
                        if (view) return view;
                        if (this.$typeClass) {
                            view = this.$typeClass?._fieldViews?.[this.defaultViewAttr]?.[0] || this.$typeClass.$typeClass?._fieldViews?.[this.defaultViewAttr]?.[0];
                            if (view) return view;
                            if (!this.isTable) {
                                if (!this.$typeClass.isAbstract) {
                                    return 'odant-field-link';
                                }
                                else if (this.$typeClass.$typeClass) {

                                    //return получить представление или добавить в $typeClass._fieldViews заранее
                                }
                            }
                        }
                        if (this.isTable)
                            return 'odant-field-table-tools';
                        return 'odant-field-default';
                    },
                    set(n) {
                        if (!this.data[this.defaultViewAttr] && !n) return;
                        if (this.data[this.defaultViewAttr] !== n) {
                            const viewFromType = this.$typeClass?._fieldViews?.[this.defaultViewAttr]?.[0] || this.$typeClass?.$typeClass?._fieldViews?.[this.defaultViewAttr]?.[0];
                            if (['odant-field-default', 'odant-field-table-tools', 'odant-field-link', viewFromType].includes(n)) {
                                this.data[this.defaultViewAttr] = undefined;
                                return;
                            }
                        }
                        this.data[this.defaultViewAttr] = n;
                    }
                },
                extendedView: {
                    $group: 'editors',
                    $editor: '@lib/field-control-editor[odant-field-control-editor]',
                    get $list() {
                        return this?.$typeClass?._fieldViews[this.extendedViewAttr] || [];
                    },
                    get() {
                        let view = this.data[this.extendedViewAttr];
                        if (view) return view;
                        if (this.$typeClass && !(this.$typeClass instanceof Promise)) {
                            view = this.$typeClass?._fieldViews?.[this.extendedViewAttr]?.[0] || this.$typeClass?.$typeClass?._fieldViews[this.extendedViewAttr]?.[0];
                            if (view) return view;
                            if (!this.isTable) {
                                if (!this.$typeClass.isAbstract)
                                    return '';
                                if (this.hasFields)
                                    return 'odant-editor-structure';
                                else
                                    return '';
                            }
                        }
                        if (this.isTable)
                            return 'odant-field-table';
                        if (this.hasFields)
                            return 'odant-editor-structure';
                        return '';
                    },
                    set(n) {
                        this.data[this.extendedViewAttr] = n;
                    }
                },
                valueExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    get() {
                        return (this.data['meta:value-expr'] || this.data.XValue);
                    },
                    set(n) {
                        this.readOnlyExpr = this.readOnlyExpr || !!n;
                        this.data.XValue = this.data['meta:value-expr'] = n;
                    },
                },
                readOnlyExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    $list: ['True', 'False'],
                    get() {
                        return (this.data['meta:read-only-expr'] || this.data.ReadOnly);
                    },
                    set(n) {
                        this.data.ReadOnly = this.data['meta:read-only-expr'] = n;
                    },
                },
                nodeConstructorExpr: {
                    $type: String,
                    $group: 'node-expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    get() {
                        return (this.data['meta:node-constructor-expr'] || this.data.XNode);
                    },
                    set(n) {
                        this.data.XNode = this.data['meta:node-constructor-expr'] = n;
                    },
                },
                nodeConstructorConditionExpr: {
                    $type: String,
                    $group: 'node-expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    get() {
                        return (this.data['meta:node-constructor-condition-expr'] || this.data.XNodeFilter);
                    },
                    set(n) {
                        this.data.XNodeFilter = this.data['meta:node-constructor-condition-expr'] = n;
                    },
                },
                validateValueExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    get() {
                        return (this.data['meta:validate-value-expr'] || this.data.Validate);
                    },
                    set(v) {
                        this.data.Validate = this.data['meta:validate-value-expr'] = v;
                    },
                },
                hiddenExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',
                    $list: ['True', 'False'],
                    get() {
                        return (this.data['meta:hidden-expr'] || this.data.HideOnEdit);
                    },
                    set(n) {
                        this.data.HideOnEdit = this.data['meta:hidden-expr'] = n;
                    },
                },
                necessaryExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-expression-editor]',

                    $list: ['True', 'False'],
                    get() {
                        return (this.data['meta:necessary-expr'] || this.data.Necessary);
                    },
                    set(n) {
                        this.data.Necessary = this.data['meta:necessary-expr'] = n;
                    },
                },
                valueListExpr: {
                    $type: String,
                    $group: 'expressions',
                    $editor: '@lib/expression-editor[odant-value-list-expression-editor]',
                    get() {
                        return (this.data['meta:value-list-expr'] || this.data.ValueList);
                    },
                    set(n) {
                        this.data.ValueList = this.data['meta:value-list-expr'] = n;
                    },
                },
            },
            get isChanged() {
                return this.$owner.isChanged;
            },
            set isChanged(v) {
                this.$owner.isChanged = v;
            },
            get abstract() {
                return this.data?.abstract || false;
            },
            get mixed() {
                return this.data.mixed || false;
            },
            get order() {
                if (this.abstract) return this.mixed ? 1 : 0;
                return this.mixed ? 2 : 3;
            },
            get defaultViewAttr() {
                return this.isTable ? 'field-toolbar' : 'field-input';
            },
            get extendedViewAttr() {
                return this.isTable ? 'field-table' : 'field-control';
            },
            get icon() {
                if (this.isTable) {
                    return (this.hasFields) ? 'odant:grid' : 'icons:menu';
                }
                if (this.$typeClass?.isAbstract === false)
                    return 'icons:link';
                if (this.hasFields)
                    return 'fields';
                return this.$typeClass?.icon || 'av:stop';
            },
            get subIcon() {
                if (this.$typeClass?.isAbstract) {
                    if (!this.hasFields && !this.isTable)
                        return '';
                }
                return this.$typeClass?.icon;
            },
            get $index() {
                return this.$owner?.$index;
            },
            get $folder() {
                return (this.$typeClass || this.$storage).$folder;
            },
            get $storage() {
                return this.$owner.$storage;
            },
            get url() { return this.$owner.url; },
            get color() {
                return `var(--inherit-${this.inherit})`
            },
            get fill() {
                return (this.isTable) ? 'blue' : this.color;
            },
            get $bodyOwner() {
                return this.$index || this.$storage || this.$base;
            },
            get path() {
                const ownerXPath = this.$owner instanceof odaField && this.$owner?.path?.replace('@', '');
                return `${ownerXPath ? ownerXPath + '/' : ''}@${this.name}`;
            },
            get hasFields() {
                return this.$typeClass?.hasFields || this.data?.$ATTR?.length > 0;
            },
            get hasChildren() { return this.hasFields; },
            $$fields: {
                $type: Array,
                get() {
                    return this.cache('$$fields', async () => {
                        let fields = this.data.$ATTR?.filter(a => (!toBool(a.hide) && !toBool(a.Hide)) || a.system).map(async data => {
                            // привязка data.proxy к имени поля, а не индексу элемента поля
                            const nameAttr = data.name ? 'name' : 'Name';
                            const newData = this.data.$ATTR[`@${nameAttr} = '${data[nameAttr]}'`];
                            const f = await odaField.build(newData, this);
                            await f.$$handlers;
                            return f;
                        }) || [];
                        fields = await Promise.all(fields);
                        if (this.$typeClass?.hasFields) {
                            let classFields = await this.$typeClass.$FIELDS?.$$fields || [];
                            if (this.isTable) {
                                if (!fields.length && this.$typeClass.isAbstract === false) {
                                    const f = await odaField.build({ name: 'link', label: this.$typeClass?.label || this.type, type: this.type, virtual: true }, this);
                                    fields.push(f);
                                }
                            }
                            else {
                                await Promise.all(classFields.map(async i => {
                                    if (!fields.find(f => f.id === i.id)) {
                                        const data = Object.assign({}, i.data);
                                        const f = await odaField.build(data, this);
                                        fields.push(f);
                                    }
                                }));
                            }
                        }
                        return fields;
                    })
                }
            },
            get data() {
                return this.draft;
            },
            getRelativePath($field) {
                if (!$field || !$field.path) {
                    return null;
                }

                let res = $field.path;
                let cnt_id = this.path;
                while (cnt_id.indexOf("/") > -1) {
                    res = res.replace(cnt_id + "/", '');
                    cnt_id = cnt_id.substring(0, cnt_id.lastIndexOf("/"));
                }
                return res;
            },
            async findFieldByPath(path) {
                const items = await this.$$fields;
                for (const item of items) {
                    if (item.path === path)
                        return item;

                    if (item.path.split('/').length > path.split('/').length)
                        break;

                    if (path.startsWith(item.path.replace('@', '') + '/'))
                        return item.findFieldByPath(path);
                }
                return null;
            },
            reset() {
                this['#$$fields'] = undefined;
                this['#hasFields'] = undefined;
                this.resetCache('$$fields');
                if (this instanceof odaExtFields) {
                    this.$owner['#$$fields'] = undefined;
                    this.$owner['#hasFields'] = undefined;
                    this.$owner[`#$${this.tag}`] = undefined;
                    this.$owner.resetCache('$$fields');
                    this.$owner.fire('changed');
                }
                else {
                    this.$owner.reset?.();
                }
                this.fire('changed');
            },
            async createField(body) {
                // if (!this.data)
                //     this.$owner.data['$' + this.tag] = [{}];
                // this.data.$ATTR ??= [];
                this.data.$ATTR.push(body);
                //todo Здесь надо убрать подпрыгивания
                this.reset();
                const prePath = this.path.replace('@', '');
                this.async(() => {
                    this.$bodyOwner.fire('create-field', `${prePath ? prePath + '/' : ''}@${body.Name}`);
                    this.$bodyOwner.fire('changed');
                });
            },
            /** deleteField */
            delete() {
                const name = this.name;
                const root = this.$owner.data.$ATTR;
                const idx = root?.findIndex(i => [i.Name, i.name].includes(name)) ?? -1;
                if (~idx) {
                    if (!this.inherit.isSelf) {
                        this.hide = true;
                    }
                    else {
                        root.splice(idx, 1);
                    }
                    // this.$owner.body = undefined;
                    // this.$owner.$$fields = undefined;
                    this.$owner.reset();
                    this.$bodyOwner.isChanged = true;
                    this.async(() => {
                        this.$bodyOwner.fire('delete-field', this.path);
                        this.$bodyOwner.fire('changed');
                        this.fire('delete');
                    });
                }
            },
            getValue($object, xpath = '/@' + this.name) {
                if (!$object) return undefined;
                // 👀
                return $object instanceof odaObject
                    ? $object?.data[xpath] || null
                    : $object[this.name] || null;
            },
            getLinkNode($object, xpath = '/@' + this.name) {
                return $object?.data[xpath.replace('@', '') + '[0]'];
            },
            setValue($object, xpath = '/@' + this.name, value) {
                const linkPath = xpath.replace('@', '') + '[0]';
                if (value !== null && value !== undefined) {
                    if (typeof Blob !== 'undefined' && value instanceof Blob) {
                        const ext = (value.name.includes('.') && '.' + value.name.split('.').pop()) || '';
                        $object.save().then(async () => {
                            const $folder = await $object.$folder;
                            const $file = await $folder.saveFile(value, genGUID() + ext);
                            return this.setValue($object, xpath, $file);
                        });
                    }
                    if (value instanceof Promise) {
                        return value.then(value => {
                            return this.setValue($object, xpath, value);
                        });
                        // value = await value;
                    }
                    if (value instanceof odaItem) {
                        if (!value.body) {
                            return value.load().then(() => {
                                return this.setValue($object, xpath, value);
                            })
                        }
                        $object.data[`${linkPath}/@sys:link`] = $object.getRelativePath(value);
                        $object.data[xpath] = value.name;
                    }
                    else {
                        $object.data[xpath] = value;
                    }
                }
                else {
                    delete $object.data[xpath];
                    if (value === null) {
                        delete $object.data[xpath.replace('@', '')];
                        delete $object.data[`${linkPath}/@sys:link`];
                        delete $object.data[`${linkPath}/@update`];
                        delete $object.data[linkPath];
                        delete $object.data[xpath.replace('@', '')];
                    }
                }
                $object.recalc();
                return value;
            },
            getFormattedValue($object, xpath, format) {
                return this.getValue($object, xpath)
            },
        }) {
            static async build(draft, owner) { //odaField
                const cache = owner.itemCache[this.prototype.typeName] ??= Object.create(null);
                const id = draft.id || draft.name || draft.Name;
                let $field = cache[id];
                if (!$field) {
                    const type = draft.type || draft.Type;
                    let $typeClass;
                    if (type) {
                        $typeClass = await (owner.$base || owner.$part || owner).findItem(type, 'all');
                        if ($typeClass) {
                            if ($typeClass?.isAbstract === false) {
                                $field = cache[id] = new odaLinkField(draft, owner);
                            }
                            else {
                                const ctor = await $typeClass.getItemCtor(this.prototype.typeName);
                                $field = cache[id] = new ctor(draft, owner);
                            }
                            $field.$typeClass = $typeClass;
                        }
                    }
                    if (!$field)
                        return super.build(draft, owner);
                }
                return $field;
            }
        }
        //#endregion odaField

        //#region odaExtFields
        globalThis.odaExtFields = class odaExtFields extends odaField.ROCKS({
            $public: {
                get name() {
                    return this.tag;
                },
                get label() {
                    return '[FIELDS]';
                }
            },
            defaultView: null,
            icon: 'odant:fields',
            subIcon: '',
            type: '',
            order: 1,
            tag: 'METADATA',
            path: '',
            isSystem: true,
            get data() {
                return this.$owner.data?.['$' + this.tag]?.[0];
            }
        }) {
            constructor(draft, owner) {
                super(draft, owner);
                this['#id'] = this.name;
                this.path = this.name;
            }
        }
        globalThis.odaStaticFields = class odaStaticFields extends odaExtFields.ROCKS({
            tag: 'STATIC',
            icon: 'icons:settings',
            order: 2,
            label: 'STATIC',
            get path() {
                return this.name;
            },
        }) { }
        globalThis.odaDetailsFields = class odaDetailsFields extends odaStaticFields.ROCKS({
            get icon() {
                return this.$owner.icon;
            },
            get label() {
                return 'Details for base: ' + this.$owner.label;
            },
            order: 3,
            name: 'DETAILS'
        }) {}
        //#endregion odaExtFields

        //#region odaLinkField
        globalThis.odaLinkField = class odaLinkField extends odaField.ROCKS({
            $typeClasses: {
                $type: Array,
                get() {
                    let classes = this.moreLinks?.map(({ id }) => {
                        return (this.$base || this.$part || this).findItem(id.trim());
                    }) || [];
                    classes = Promise.all(classes);
                    return classes;
                }
            },
            getValue($object, xpath) {
                if (!$object) return undefined;
                if (!($object instanceof odaObject)) {
                    return $object[this.name];
                }
                const node = this.getLinkNode($object, xpath);
                if (!node?.['/@sys:link']) {
                    const name = this.$super('getValue', $object, xpath);
                    if (!name) return;
                    return this.restoreByName($object, xpath, name, node);
                }
                return this.restore($object, xpath, node['/@sys:link'], node);
            },
            async setValue($object, xpath, value) {
                value = await this.$super('setValue', $object, xpath, value);
                if (!value) return;

                const node = this.getLinkNode($object, xpath);
                const linkData = await value.XQuery(`element o { OBJECT/(attribute sys:last-update{ @update }, ${this.copyLinkData}) }`);

                for (const name in linkData?.$o?.[0]) {
                    const val = linkData.$o[0][name];
                    node[name] = val;
                }
                return value;
            },
            async restoreByName($object, xpath, name, node) {
                const { $cls, oid } = (await Promise.all([this.$typeClass, ...(await this.$typeClasses)].map(async c => {
                    const $index = await c.getIndex('Names');
                    const oid = await $index.XQuery(`/\/*[@name="${name}"]/string(@oid)`, { format: 'text' });
                    console.log(c.name, oid);
                    return { $cls: c, oid };
                }))).find(o => o.oid) || {};

                const $linkObj = await $cls?.getObject(oid);

                if ($linkObj) {
                    if (!node['sys:last-update'])
                        return this.setValue($object, xpath, $linkObj);
                }
                return $linkObj || null;
            },
            async restore($object, xpath, linkPath, node) {
                const $storage = linkPath.includes('C:') ? this.$base : $object.$class.$topClass;
                const $linkObj = await $storage.findItem(linkPath);
                if ($linkObj) {
                    if (!$linkObj.body)
                        await $linkObj.load();
                    if (!node['sys:last-update'] || (this.linkType === 'Dynamic' && Date.parse(node['sys:last-update']) < Date.parse($linkObj.data.update))) {
                        return this.setValue($object, xpath, $linkObj);
                    }
                }
                return $linkObj;
            },
            $public: {
                moreLinks: {
                    $group: 'link',
                    $editor: '@lib/type-editor[odant-type-array-editor]',
                    $type: Array,
                    get() {
                        return this.data['more-links']?.split?.('\n').filter(i => i).map(i => {
                            return new odaMoreType(i, this, 'moreLinks');
                        });
                    },
                    set(v) {
                        this.data['more-links'] = v.map(i => i.id).filter(i => i).join('\n');
                    },
                },
                // extendedView: 'odant-field-link-ext',
                copyLinkData: {
                    $group: 'link',
                    get() {
                        return (this.data['copy-link-data'] || this.data.LinkFields) || '@*';
                    },
                    set(n) {
                        this.data['copy-link-data'] = this.data.LinkFields = n;
                    }
                },
                linkType: {
                    $group: 'link',
                    $list: ['Dynamic', 'Static'],
                    get() {
                        return this.data['link-type'] || this.data['LInkType'] || 'Static';
                    },
                    set(n) {
                        this.data['link-type'] = this.data['LInkType'] = n;
                    }
                }
            }
        }) {
            static defaultView = 'odant-field-link';
        }
        //#endregion odaLinkField
    }

    //#region odaMoreType
    globalThis.odaMoreType = class odaMoreType extends ROCKS({
        $editor: '@lib/type-editor[odant-type-more-editor]',
        hideExpander: true,
        path: '',
        $typeClass: {
            $type: Object,
            get() {
                const item = this.owner.$base || this.owner.$part || this.owner.$class;
                return item.findItem(this.path);
            }
        },
        delete() {
            this.owner[this.propName].remove(this);
            this.owner[this.propName] = [...this.owner[this.propName]];
        },
        id: {
            $type: String,
            get() {
                return this.$typeClass?.id;
            }
        }
    }) {
        constructor(id, owner, propName) {
            super();
            this.id = id;
            this.path = id;
            this.owner = owner;
            this.propName = propName;
        }
    }
    //#endregion odaMoreType

    //#region odaMorePack
    globalThis.odaMorePack = class odaMorePack extends ROCKS({
        $editor: '@lib/packs-editor[odant-packs-editor-item]',
        hideExpander: true,
    }) {
        constructor(data, owner) {
            super();
            this.data = data;
            this.owner = owner;
        }
    }
    //#endregion odaMorePack

    //#region odaServerItem
    globalThis.odaServerItem = class odaServerItem extends odaItem.ROCKS({
        typeName: 'server-item',
        $public: {
            defaultView: {
                $def() {
                    return this.getDefHandler('view');
                },
                $group: 'Controls',
                $editor: '@lib/select-handler-editor[odant-select-view-editor]',
                get() {
                    return this.getDefHandler('view');
                },
                set(n) {
                    this.setDefHandler('view', n);
                }
            },
            defaultPage: {
                $def() {
                    return this.getDefHandler('page');
                },
                $group: 'Controls',
                $editor: '@lib/select-handler-editor[odant-select-page-editor]',
                get() {
                    return this.getDefHandler('page');
                },
                set(n) {
                    this.setDefHandler('page', n);
                }
            },
            defaultForm: {
                $def() {
                    return this.getDefHandler('form');
                },
                $group: 'Controls',
                $editor: '@lib/select-handler-editor[odant-select-page-editor]',
                get() {
                    return this.getDefHandler('form');
                },
                set(n) {
                    this.setDefHandler('form', n);
                }
            },
        },
        get data() {
            const data = attachData.call(this);
            return data;
        },
        get version() { return this.data.v ?? this.data.version ?? 0; },
        get url() {
            return this.$host.origin + '/api' + this.path;
        },
        get $folder() {
            return odaFolder.build({ id: '~', name: '~' }, this);
        },
        get xml() {
            return this.request('', { format: 'xml' });
        },
        saveFile(...args) {
            return this.$folder.then(f => {
                return f.saveFile(...args);
            })
        },
        async delete() {
            return getBool((await this.request('delete'))?.result);
        },
        XQuery(xq, { format = 'json', mask = '*' } = {}) {
            return this.request('xquery', { format, mask, loadmask: mask/* , async: true */ }, xq)
        },
        request(method = '', params, postObject) {
            if (postObject)
                return CORE.request(this.url, method, params, postObject);
            const key = 'request: ' + method + ' ' + (params ? JSON.stringify(params) : '');
            //todo: убрать костыль
            return ['create', 'add'].some(s => method.includes(s))
                ? CORE.request(this.url, method, params)
                : this.cache(key, async () => {
                    try {
                        return await CORE.request(this.url, method, params);
                    }
                    finally {
                        this.resetCache(key);
                    }
                });
            // return CORE.request(this.url, method, params);
        },
        load(params) {
            return this.request('', params).then(res => {
                const key = res && Object.keys(res)[0];
                (this.body = res?.[key][0] || null);
                this.data = undefined;
                return this.body;
            })
            .catch(err => {
                console.warn(`Error on load ${this.typeName} "${this.name}"`, err);
                this.body = null;
                this.data = undefined;
                return this.body;
            })
        },
        async save(data, params = {}) {
            if (data || this.body)
                await this.request(undefined, params, data || { [this.rootTag]: this.body });
            this.isChanged = false;
            this.isNew = false;

            this.resetHandlers();

            this.fire('after-save', this);
        },
        resetHandlers() {
            this.$$handlers = undefined;
            this.__handlersCache = undefined;
            this.itemCache = {};
        },
        findItem(path, scope) {
            if (path) {
                return this.cache('findItem:' + path, async () => {
                    if (path.startsWith('~')) {
                        path = this.path + '/' + path;
                    }
                    else if (!path.includes(':')) {
                        path = 'H:*/P:*/B:*/C:' + path;
                    }
                    const p = await this.findItemPath(path, scope);
                    if (!p) return null;
                    const list = p.split('/');
                    list.shift();
                    let step;
                    let curItem = this.$host;
                    while (step = list.shift()) {
                        curItem = await curItem.getItem(step);
                    }
                    return curItem;
                })
            }
            return null;
        },
        getItem(step, draft = {}) {
            return this.cache('getItem:' + step, async () => {
                if (step === '~')
                    return this.$folder;
                if (step[1] === ":") {
                    const prefix = step[0];
                    let id = step.substring(2);
                    switch (step[0]) {
                        case 'O': {
                            draft.id = id
                            return odaObject.build(draft, this);
                        }
                        case 'I':
                            return this.getIndex(id);
                        case 'H': {
                            if (id === CORE.host.id)
                                return CORE.host;
                            const host = new odaHost({ origin: CORE.host.origin, id });
                            const config = await host.config;
                            host._$extends = config?.$extends;
                            host.draft = Object.assign({}, config);
                            return host;
                        }
                        case this.prefix:
                            if (id && id === this.id)
                                return this;
                    }
                    const config = await this.config;
                    if (!config) {
                        console.error(`${this.typeName} "${this.label}" has empty config`);
                        return null;
                    }
                    draft = config['$' + prefix]?.find(i => i.id === id) || { id, prefix, available: false };
                    const cls = ('oda-' + prefixMap[prefix]).toCamelCase();
                    return globalThis[cls].build(draft, this);
                }
                return this.getFileItem(step);
            })
        },
        findItemPath(path, scope = 'context') {
            return this.request('find_item', { prefix: true, chain: 'full', path, scope }).then(res => {
                return res?.result;
            })
        },
        async _getFromPath(path, isFile) {
            const steps = path.split('/');
            const name = steps.pop();
            path = steps.join('/');
            const items = await this[isFile ? 'getFiles' : 'getFolders'](path ? path + '/*' : '*');
            return items.find(i => i.name === name);
        },
        executeServerHandler(name, params, content) {
            return this.request(`execute=${name}`, params, content);
        },
        contains(item) {
            if (item === this) return false;
            while (item && item !== this) {
                item = item.$owner;
            }
            return !!item;
        },
    }) {
        constructor() {
            super(...arguments);
            this.created?.();
        }
        static defaultView = 'editor';
        static defaultPage = 'navigator';
        static defaultForm = 'form';
    }
    globalThis.odaStructureItem = class odaStructureItem extends odaServerItem.ROCKS({
        typeName: 'structure',
        getItemCtor(type, ext) {
            return this._itemCtors[ext || type] ??= this.joinCtors(type, ext);
        },
        async joinCtors(type, ext) {
            ext = ext?.toLowerCase();
            const _extendClass = (ctor, extend) => {
                const name = this instanceof odaHost
                    ? extend.name
                    : `${ext ? ext : type}-${this.id}`.toCamelCase()
                const code = `return class ${name} extends this.ROCKS(extend.__rocks__.prototype) {}`;
                const fn = new Function('extend', code);
                return fn.call(ctor, extend);
            };
            const defCtor = typeMap[type];
            let ctor;
            if (this.$owner)
                ctor = await this.$owner.getItemCtor(type, ext);
            else
                ctor = typeMap[type];

            if (await this.$parent) {
                const next = await this.$parent.getItemCtor(type, ext);
                if (next !== ctor) {
                    if (ctor === defCtor)
                        ctor = next;
                    else if (next !== defCtor)
                        ctor = _extendClass(ctor, next);
                }
            }

            let types = (await this.$types || []).map(cls => {
                return cls.getItemCtor(type, ext);
            });

            if (types.length) {
                types = await Promise.all(types);
                ctor = types.reduce((res, next) => {
                    if (next !== res) {
                        if (res === defCtor)
                            res = next;
                        else if (next !== defCtor)
                            res = _extendClass(res, next);
                    }
                    return res;
                }, ctor);
            }

            const exts = this instanceof odaServerItem ? this._$extends?.[0] : undefined;
            const ctors = [];
            if (exts) {
                let proto = defCtor;
                while (proto?.prototype) {
                    const type = proto.prototype.typeName;
                    if (!type) break;
                    try {
                        let module;
                        if (ext && exts[ext])
                            module = await import(this.url + '/web/' + ((this instanceof odaHost) ? 'core/' : '') + type + '/extentions/' + ext.substring(1) + '/' + type + '.js');
                        else if (exts[type])
                            module = await import(this.url + '/web/' + ((this instanceof odaHost) ? 'core/' : '') + type + '/' + type + '.js');
                        if (module) {
                            if (!module.default)
                                throw new Error('corrupt module');
                            let extend = module.default;
                            if (!(extend instanceof Function)) {
                                extend = class x extends ctor.ROCKS(extend) { };
                                if (exts[ext]) {
                                    renameClass(extend, ctor.name + ext);
                                }
                            }
                            ctors.add(extend);
                            if (ext) break;
                        }
                    }
                    catch (e) {
                        console.error(e);
                    }
                    proto = proto?.__proto__?.__proto__;
                }
                while (proto = ctors.pop()) {
                    ctor = _extendClass(ctor, proto);
                }
            }
            return ctor;
        },
        get handlerConfig() {
            return this.config.then(cfg => {
                const my = cfg.$handlers?.[0];
                const own = this.$owner?.handlerConfig;
                return Promise.all([my, own]).then(p => {
                    const myMap = p[0];
                    let ownMap = p[1];
                    if (myMap) {
                        let type = this.typeName;
                        ownMap = structuredClone(ownMap || myMap);
                        Object.keys(myMap).forEach(side => {
                            const mySide = myMap[side][0];
                            let ownSide = ownMap[side] ??= [];
                            ownSide = ownSide[0] ??= Object.create(null);
                            Object.keys(mySide).forEach(type => {
                                const myType = mySide[type][0];
                                let ownType = ownSide[type] ??= [];
                                ownType = ownType[0] ??= Object.create(null);
                                Object.assign(ownType, myType);
                            });
                        });
                    }
                    return ownMap;
                });
            });/*.then(res=>{
                let type = this.typeName;
                Object.values(res).forEach(part=>{
                    part

                })
                return res;
            })*/
        },
        getHandlerCtor(type, ext, path) {
            return (this._itemCtors[`${type}${ext || ''}${path}`] ??= this.newJoinHandlerCtors(type, ext, path));
        },
        //todo подключить когда Аркадий добавит слой SIDE
        async newJoinHandlerCtors(type, ext, path) {
            const handlerType = path?.split('/')[2] || 'handler';
            const handlerName = path?.split('/').pop().split('.')[0] || 'no-name';

            const _extendClass = (ctor, extend) => {
                const fn = new Function('extend', `return class ${(handlerType + '-' + handlerName + '-' + this.id).toCamelCase()} extends this.ROCKS(extend.__rocks__.prototype){}`);
                return fn.call(ctor, extend);
            };

            const defCtor = typeMap[handlerType];

            let ctor;
            if (this.$owner) {
                ctor = await this.$owner.getHandlerCtor(type, ext, path);
            }
            else
                ctor = typeMap[handlerType];

            if (await this.$parent) {
                const next = await this.$parent.getHandlerCtor(type, ext, path);
                if ((next !== ctor) && !next.isErrorCtor) {
                    if (ctor === defCtor)
                        ctor = next;
                    else if (next !== defCtor)
                        ctor = _extendClass(ctor, next);
                }
            }

            let types = (await this.$types || []).map(cls => {
                return cls.getHandlerCtor(type, ext, path);
            });

            if (types.length) {
                types = await Promise.all(types);
                ctor = types.reduce((res, next) => {
                    if ((next !== res) && !next.isErrorCtor) {
                        if (res === defCtor)
                            res = next;
                        else if (next !== defCtor)
                            res = _extendClass(res, next);
                    }
                    return res;
                }, ctor);
            }

            if (await this.$source) {
                const next = await this.$source.getHandlerCtor(type, ext, path);
                if ((next !== ctor) && !next.isErrorCtor) {
                    if (ctor === defCtor)
                        ctor = next;
                    else if (next !== defCtor)
                        ctor = _extendClass(ctor, next);
                }
            }

            const cfg = await this.config;
            const handlers = cfg?.$handlers?.[0]?.['$' + SIDE]?.[0];
            if (handlers) {
                const ctors = [];
                let proto = typeMap[type];
                while (proto?.prototype) {
                    const type = proto.prototype.typeName;
                    if (!type)
                        break;
                    const baseUrl = this.url + '/web/' + ((this instanceof odaHost) ? 'core/' : '');
                    const sources = [
                        [(handlers['$_' + ext] || []), `${baseUrl}file/extentions/${ext}${path}`],
                        [(handlers['$' + type] || []), `${baseUrl}${type}${path}`]
                    ];
                    for (const [paths, url] of sources) {
                        if (paths.find(p => (('/' + SIDE + '/' + p.path) === path))) {
                            try {
                                const module = await import(url);
                                if (module) {
                                    if (!module.default)
                                        throw new Error('corrupt module');
                                    let extend = module.default;
                                    if (!(extend instanceof Function))
                                        extend = class extends ctor.ROCKS(extend) { };
                                    ctors.add(extend);
                                }
                            }
                            catch (e) {
                                console.error(handlerType, path, 'for: ' + type, e);
                                const errorCtor = class extends odaHandlerError{ static error = e };
                                return errorCtor;
                            }
                        }
                    }
                    proto = proto?.__proto__?.__proto__;
                }
                while (proto = ctors.pop()) {
                    ctor = _extendClass(ctor, proto);
                }
            }
            return ctor;
        },
        // async joinHandlerCtors(type, path){

        //     const handlerType = path.split('/')[2];
        //     const handlerName = path.split('/').pop().split('.')[0];

        //     const _extendClass = (ctor, extend)=>{
        //         const fn = new Function('extend', `return class ${(handlerType + '-' + handlerName + '-' + this.id).toCamelCase()} extends this.ROCKS(extend.__rocks__.prototype){}`);
        //         return fn.call(ctor, extend);
        //     }


        //     const defCtor = typeMap[handlerType];

        //     let ctor;
        //     if (this.$owner)
        //         ctor = await this.$owner.getHandlerCtor(type, path);
        //     else
        //         ctor = typeMap[handlerType];

        //     if (await this.$parent){
        //         const next = await this.$parent.getHandlerCtor(type, path);
        //         if(next !== ctor){
        //             if(ctor === defCtor)
        //                 ctor = next;
        //             else if(next !== defCtor)
        //                 ctor = _extendClass(ctor, next);
        //         }
        //     }

        //     let types = (await this.$types || []).map(cls=>{
        //         return cls.getHandlerCtor(type, path);
        //     })

        //     if(types.length){
        //         types = await Promise.all(types);
        //         ctor = types.reduce((res, next)=>{
        //             if(next !== res){
        //                 if(res === defCtor)
        //                     res = next;
        //                 else if(next !== defCtor)
        //                     res = _extendClass(res, next);
        //             }
        //             return res;
        //         }, ctor);
        //     }

        //     const cfg = await this.config;
        //     if(cfg.$handlers){
        //         const ctors = [];
        //         const handlers = cfg.$handlers[0];
        //         let proto = typeMap[type];
        //         while (proto?.prototype){
        //             const type = proto.prototype.typeName;
        //             if(!type)
        //                 break;
        //             const paths = handlers['$'+type];
        //             if(paths){
        //                 if(paths.find(p=>(p.path === path))){
        //                     try{
        //                         const url = (this.url + '/web/' + ((this instanceof odaHost)?'core/':'')  + type + path);
        //                         let module = await import(url);
        //                         if(module){
        //                             if(!module.default)
        //                                 throw new Error ('corrupt module');
        //                             let extend = module.default;
        //                             if (!(extend instanceof Function))
        //                                 extend = class extends ctor.ROCKS(extend){};
        //                             ctors.add(extend)
        //                         }
        //                     }
        //                     catch (e) {
        //                         console.error(handlerType, path, 'for: ' + type,e)
        //                     }
        //                 }
        //             }
        //             proto = proto?.__proto__?.__proto__;
        //         }
        //         while (proto = ctors.pop()){
        //             ctor = _extendClass(ctor, proto);
        //         }
        //     }
        //     return ctor;
        // },
        draft: undefined,
        get config() {
            return this.loadConfig(2);
        },
        get hasChildren() {
            return this.draft.childs === 'True';
        },
        get access() {
            return new odaAccess(+(this.draft.access ?? 0), this);
        },
        loadConfig(deep = 2) {
            return this.cache('load-config:' + deep, () => {
                return this.request('config', { prefix: true, deep }).then(config => {
                    config = config?.['$' + this.prefix]?.[0];
                    this._$extends = config?.$extends;
                    this.draft = Object.assign({}, config);
                    this.resetCache('load-config:' + deep);
                    return config;
                })
            })
        },
        async getItems(prefix) {
            if (!this.hasChildren) return [];
            try {
                const config = await this.config;
                if (config) {
                    const items = config['$' + prefix] || [];
                    return Promise.all(items.map(i => this.getItem(prefix + ':' + i.id)));
                }
            }
            catch (e) {
                console.error(e)
            }
        },
        HANDLERS: {
            $group: 'HANDLERS',
            get _handlerStructure() {
                return this._handlerMap.then(map => {
                    const res = {};
                    for (let part of map.values()) {
                        part = part[SIDE]?.[0];
                        if (part) {
                            Object.keys(part).forEach((type) => {
                                const typeHandlers = part[type]?.[0];
                                Object.keys(typeHandlers).forEach((prop) => {
                                    if (prop === 'H') {
                                        const existValue = res?.[type]?.[0]?.[prop];
                                        const propValue = (existValue?.length)
                                                          ? typeHandlers[prop]?.filter(n => !existValue.some(e => (e.id === n.id)))
                                                          : typeHandlers[prop];
                                        if (propValue?.length) {
                                            res[type] ??= [{}]
                                            res[type][0][prop] ??= [];
                                            res[type][0][prop].push(...propValue);
                                        }
                                    } else {
                                        const existValue = res?.[type]?.[0]?.[prop]?.[0]?.H;
                                        const propValue = (existValue?.length && typeHandlers[prop]?.[0]?.H)
                                                          ? typeHandlers[prop][0].H.filter(n => !existValue.some(e => (e.id === n.id)))
                                                          : typeHandlers[prop]?.[0]?.H || [];
                                        if (propValue?.length) {
                                            res[type] ??= [{}]
                                            res[type][0][prop] ??= [{ H: [] }];
                                            res[type][0][prop][0].H.push(...propValue);
                                        }
                                    }
                                });
                            });
                        }
                    }
                    return res;
                })
            },
            get _handlerMap() {
                const url = this.prefix === 'H' ? '/web/core' : '/~/web';
                const xq = `\
declare function local:func($item as node()*, $type as xs:string, $owner as xs:string?) as node()* {
    for $h_file in $item/DIR/FILE[@id=concat(../@id, '.js')]
    let $h_dir := $h_file/..
    let $hierarchy  := string-join(($owner, $h_dir/@id), '-')
    let $srcId := if (string-length($h_file/@source) > 15)
        then oda:right($h_file/@source, 15)
        else ()
    let $children := local:func($h_dir, $type, $hierarchy)
    return element H {
        $h_file/(@inherit, @access, @source ),
        attribute path        { substring($h_dir/@path, 6) },
        attribute name        { $h_dir/@id },
        attribute hierarchy   { $hierarchy },
        attribute type        { $type },
        attribute loadUrl     { $h_file/@path },
        attribute category    { $h_dir/../@id },
        attribute sourceId    { $srcId },
        attribute id          { if ($srcId) then concat($h_dir/@id, '-', $srcId) else $h_dir/@id },
        attribute hasChildren { if ($children) then 'true' else 'false' },
        $children
    }
};

for $side in ('client', 'server')
return element { $side } {
    for $item in oda:select(DIR, 'id', 'host|part|base|module|workplace|domain|class|field|file|folder|handler|index|object', '|')
    let $type := $item/@id
    let $handlers := local:func($item/DIR[@id=$side]/DIR, $type, nil)
    return if (count($handlers) > 0)
    then (
        element { $type } {
            $handlers,
            if ($type = 'file')
            then (
                for $ext in $item/DIR[@id='extentions']/DIR
                let $extName := $ext/@id
                let $extHandlers := local:func($ext/DIR[@id=$side]/DIR, $type, nil)
                return if (count($extHandlers) > 0)
                then (
                    element { $extName } {
                        $extHandlers
                    }
                )
                else ()
            )
            else ()
        }
    )
    else ()
}`;

                const handlers = CORE.request(this.url + url, 'get_dirlist', { deep: 7, format: 'json-2' }, xq).then(async d => {
                    const result = new Map();
                    result.set(this, d);
                    if (this.$base) {
                        let exp = await this.$base._exportHandlersMap;
                        for (let m of exp) {
                            if (!result.has(m[0])) {
                                result.set(m[0], m[1]);
                            }
                        }
                    }
                    return result;
                });
                return handlers;
            },
        }
    }) {}
    //#endregion odaStructureItem

    //#region odaWorkplaceOwner
    globalThis.odaWorkplaceOwner = class odaWorkplaceOwner extends CORE({
        get $$workplaces() {
            return this.getItems('W');
        },
        addWorkplace(source) {
            return this._createDomain(source, 'W');
        },
    }) {}
    //#endregion odaWorkplaceOwner

    //#region odaModuleOwner
    globalThis.odaModuleOwner = class odaModuleOwner extends CORE({
        get $$modules() {
            return this.getItems('M');
        },
        addModule(source) {
            if (this instanceof odaModule) return;
            return this._createDomain(source, 'M');
        },
    }) {}
    //#endregion odaModuleOwner

    //#region odaHost
    globalThis.odaHost = class odaHost extends odaStructureItem.ROCKS({
        get $user() {
            return null;
        },
        get $module() {
            return undefined;
        },
        isSystem: true,
        readOnly: true,
        hasChildren: true,
        icon: 'device:devices',
        typeName: 'host',
        prefix: 'H',
        // TODO удалить, после появления серверного кэша
        $$hosts: [],
        findAccount(login) {
            return this.request('find_account', { login });
        },
        get config() {
            return this.loadConfig(3);
        },
        $root: {
            $def: null,
            get() {
                return this.getItem('P:ROOT');
            }
        },
        $public: {
            $def: null,
            get() {
                return this.getItem('P:PUBLIC');
            }
        },
        $support: {
            $type: Object,
            get() {
                return this.draft.support && this.getItem('H:' + this.draft.support);
            }
        },
        $licenses: {
            $type: Object,
            get() {
                return this.findItem(this.path + '/P:SYSTEM/C:***************');
            }
        },
        get url() {
            return this.origin + '/api' + (this.id ? '/H:' + this.id : '');
        },
        get $host() { return this; },
        get $$users() {
            return getUsers.call(this);
        },
        get $$assignedUsers() {
            return this.$$users.then($$users => $$users.filter(u => u.assigned));
        },
        get $$unassignedUsers() {
            return this.$$users.then($$users => $$users.filter(u => !u.assigned));
        },
        get $$parts() {
            return this.getItems('P');
        },
        get EventSource() {
            return this.cache('EventSource', () => {
                const parser = new URL(this.url);
                let url = (parser.protocol === 'https:')
                    ? 'wss://'
                    : 'ws://';

                url += parser.hostname;

                if (parser?.port?.length > 0) {
                    url += ':' + parser.port;
                }

                url += '/ws';
                return new WebSocketEvents(url);
            });
        },
        async created() {
            this.origin = this.data.origin;
        },
        getFullId(item) {
            let s = '';
            let own = item;
            let pref = '';
            while (own) {
                let step = own.id || null;
                if (step && own.prefix) {
                    let convertedPrefix = own.prefix;
                    switch (convertedPrefix) {
                        case 'B':
                        case 'M':
                        case 'P':
                        case 'S':
                        case 'W':
                            convertedPrefix = 'D';
                            break;
                    }
                    step = convertedPrefix + ':' + step;
                }
                if (step) {
                    step = '/' + step;
                    if (!own.prefix || own.prefix !== pref) {
                        s = step + s;
                    }
                }
                pref = own.prefix;
                own = own.$owner;
            }
            return s;
        },
        async assignUsers(ids) {
            const body = ids.join(' ');
            if (body) {
                const res = await this.request('add_users', {}, body);
                this.$$users = undefined;
                this.$$assignedUsers = undefined;
                this.$$unassignedUsers = undefined;
                this.fire('changed');
                return res;
            }
        },
        async unassignUsers(ids) {
            const body = ids.join(' ');
            if (body) {
                const res = await this.request('remove_users', {}, body);
                this.$$users = undefined;
                this.$$assignedUsers = undefined;
                this.$$unassignedUsers = undefined;
                this.fire('changed');
                return res;
            }
        },
        addListen(item, callback) {
            this.EventSource.addEventListener(this.getFullId(item), callback, false);
        },
        remListen(item, callback) {
            this.EventSource.removeEventListener(this.getFullId(item), callback);
        },
        addCustomListen(item, name, callback) {
            this.EventSource.addCustomListener(`${this.getFullId(item)}|${name}`, callback, false);
        },
        remCustomListen(item, name, callback) {
            this.EventSource.removeCustomListener(`${this.getFullId(item)}|${name}`, callback);
        },
        fireServerEvent(item, name, eventObject) {
            this.EventSource.sendCustomEvent(`${this.getFullId(item)}|${name}`, eventObject);
        },
        async logout() {
            try {
                await CORE.request('/api?method=logout');
            }
            catch (err) {
                console.error(err);
            }
            localStorage.removeItem('security');
            location.reload();
        },
        getUserLabel(userId) {
            return this.cache('getUserLabel:' + userId, () => {
                return CORE.request(`${this.url}/P:WORK/U:${userId}`, 'get_user_label').then(res => {
                    return res?.result;
                })
            });
        },
        getUserIcon(userId) {
            return this.cache('getUserIcon:' + userId, () => {
                return CORE.request(`${this.url}/P:WORK/U:${userId}`, 'get_user_icon').then(res => {
                    return res?.result;
                })
            });
        },
        load() {
            return this.body;
        },
        get data() {
            return this.body;
        },
        get body() {
            return this.draft || undefined;
        }
    }) {
        static defaultView = 'desktop';
        static defaultPage = 'navigator';
        static defaultForm = 'form';
    }
    //#endregion odaHost

    //#region odaStorage
    globalThis.odaStorage = class odaStorage extends globalThis.odaStructureItem.ROCKS({extends: [odaFieldOwner],
        get exportConfig() {
            const next = this.$$classes.then(classes => Promise.all(classes.map(cls => cls.exportConfig)).then(r => r.filter(Boolean)));
            const current = this.config.then(r => r.$handlers?.[0]);
            return Promise.all([current, next]).then(res => {
                const myMap = structuredClone(res[0] || Object.create(null));
                Object.keys(myMap).forEach(side => {
                    side = side.slice(1);
                    myMap[side] = myMap['$' + side];
                    delete myMap['$' + side];

                    const mySide = myMap[side][0];
                    Object.keys(mySide).forEach(type => {
                        type = type.slice(1);
                        mySide[type] = mySide['$' + type];
                        delete mySide['$' + type];

                        const handlers = mySide[type].map(handler => {
                            const p = handler.path.split('/');
                            // todo: временное решение, пока sub handler'ы не вложены в handler'ы
                            if (p.length > 3)
                                return;
                            const category = p[0];
                            const name = p[p.length - 2];

                            handler.export = true;
                            handler.access = this.access.level;
                            handler.category = category;
                            handler.hasChildren = false; // todo
                            handler.id = `${name}-${this.id}`;
                            handler.loadUrl = `/api${this.path}/web/${type.startsWith('_') ? `file/extentions/${type.slice(1)}` : type}/${side}/${handler.path}`;
                            handler.name = name;
                            handler.hierarchy = name;
                            handler.sourceId = this.id;

                            handler.source = this.path;
                            handler.type = type.startsWith('_') ? 'file' : type;
                            return handler;
                        });
                        mySide[type] = [{ H: handlers.filter(Boolean) }];
                        if (type.startsWith('_')) { // для расширений файлов
                            mySide['file'] ??= [{}];
                            mySide['file'][0][type.slice(1)] = mySide[type];
                            delete mySide[type];
                        }
                    })
                })
                res[1].forEach(childMap => {
                    Object.keys(childMap).forEach(side => {
                        const childSide = childMap[side][0];
                        let mySide = myMap[side] ??= [];
                        mySide = mySide[0] ??= Object.create(null);
                        Object.keys(childSide).forEach(type => {
                            const childType = childSide[type][0];
                            let myType = mySide[type] ??= [];
                            myType = myType[0] ??= Object.create(null);
                            Object.keys(childType).forEach(handlers => {
                                const childHandlers = childType[handlers];
                                myType[handlers] ??= [];
                                myType[handlers] = [...myType[handlers], ...childHandlers];
                                // let myHandlers = myType[handlers] ??= [];
                                // myHandlers = myHandlers[0] ??= Object.create(null);
                                // Object.assign(myHandlers, childHandlers);
                            })
                        })
                    })
                })
                return myMap;
            })
        },
        rootTag: '$CLASS',
        typeName: 'storage',
        fileFactory: {},
        get developmentMode() {
            return this.$base && (this.$base.id === this._originBaseId || this.$owner.developmentMode);
        },
        get _originBaseId() {
            return this.draft['sys:origin-base']?.slice?.(-15);
        },
        $originBase: {
            $type: odaItem,
            get() {
                let id = this._originBaseId;
                if (id) {
                    return this.$host.findItem('B:' + id);
                }
                return null;
            }
        },
        get $storage() {
            return this;
        },
        $public: {
            // type: {
            //     $editor: '@lib/type-editor[odant-type-editor]',
            //     get() {
            //         return (this.draft.type || this.draft.Type || '').trim();
            //     },
            //     set(v) {
            //         if (v)
            //             (this.$owner.$base || this.$owner.$part || this.$owner).findItem(v).then(c => (this.$typeClass = c));
            //         else
            //             this.$typeClass = null;
            //         this.data.type = this.data.Type = v;
            //     },
            // },

            category: {
                $type: String,
                get() {
                    return this.draft.category || '';
                },
                set(n) {
                    this.data.category = n;
                }
            },
            icon: {
                get() {
                    if (this.draft.icon) {
                        return this.draft.icon;
                    }
                    if (!this.hasChildren) {
                        return 'odant:abstract-class';
                    }
                    return odaStorage.DEFAULT_ICON;
                },
                set(v) {
                    if ((!this.hasChildren && v === 'odant:abstract-class') || (v === odaStorage.DEFAULT_ICON)) {
                        delete this.data.icon;
                    }
                    else {
                        this.data.icon = v;
                    }
                },
            },
        },
        disabled: {
            $def: false,
            get() {
                return this.$owner.disabled || (this.data?.['sys:disabled'] === 'True' ? true : false);
            },
            set(v) {
                if (this instanceof odaModule && this.$parent?.disabled === true) return;
                this.data['sys:disabled'] = v ? 'True' : undefined;
            }
        },
        checkAdd: ['class'],

        get accessLevel() { return this.draft?.accessLevel },
        set accessLevel(n) { this.request('set_access_level', { access: n }) },

        get $staticObject() { return this.getObject({ id: this.id }) },
        // get inModule() { return decodeURIComponent(this.path).includes('M:') },
        $parent: {
            $type: Object,
            get() {
                return this.findItem(this.data.parent || this.data.Parent);
            }
        },
        get $$classes() {
            return this.getItems('C');
        },
        checkIn($structureItem) {
            return false;
        },
        checkOut($structureItem) {
            return $structureItem.checkOut(this);
        },
        async eventHandler(event) {
            switch (event.type) {
                case 'class':
                case 'domain': {
                    switch (event.operation) {
                        case 'delete': {
                            this.$owner.config = undefined;
                            this.$owner.resetCache('load-config');
                            this.$owner.fire('changed');
                            this.fire('delete');
                        } break;
                        case 'update': {
                            this.config = undefined;
                            await this.config;
                            this.resetCache('load-config');
                            if (!this.isChanged) {
                                this.$$typed = undefined;
                                this.resetCache('$$fields');
                                this.body = await this.load();
                                this['#isAbstract'] = undefined;
                                this.itemCache['handler'] = undefined;
                                this.__handlersCache = Object.create(null);
                                this.handlersList = undefined;
                                this.$$handlers = undefined;
                            }
                            this.$owner.fire('changed');
                            this.fire('changed');
                        } break;
                        case 'create': {
                            if (CORE.lastCreated !== 'silent') {
                                const prefix = Object.keys(event.$additional?.last).last;
                                CORE.lastCreated = {
                                    $owner: this,
                                    id: event.$additional?.last?.[prefix]?.last?.id
                                };
                            }
                            else {
                                CORE.lastCreated = '';
                            }

                            this.config = undefined;
                            this.resetCache('load-config');
                            this.resetCache('$$typed');
                            Promise.resolve(this.config).then(config => {
                                for (const k in config) {
                                    this.draft[k] = config[k];
                                }
                                // todo: подумать, как сделать реактивным от draft
                                this.hasChildren = undefined;
                                this.fire('changed');
                            })
                        } break;
                        case 'pack create': {
                            this.changePackets++;
                            this.fire('pack-create', event.$additional);
                        } break;
                        case 'pack delete': {
                            this.changePackets++;
                            this.fire('pack-delete', event.$additional);
                        } break;
                    }
                } break;
                case 'object': {
                    if (event.$additional) {
                        event.$additional.forEach(u => {
                            if (u.$E) {
                                u.$E.forEach(e => {
                                    if (e.$P) {
                                        e.$P.forEach(async (p) => {
                                            if (p.$C) {
                                                this.fire('create', p.$C);
                                                this.fire('update', p.$C);
                                            }
                                            if (p.$U) {
                                                this.fire('update', p.$U);
                                            }
                                            if (p.$D) {
                                                this.fire('delete-objects', p.$D);
                                            }
                                        })
                                    }
                                })
                                this.resetCache('object-count');
                                this.getObjectCount?.()?.then(res => {
                                    this.count = res;
                                })
                            }
                            if (u.$C) {
                                u.$C.forEach((c) => {
                                    const cls = this.findItem('C:' + c.i);
                                })
                            }
                        });
                    }
                } break;
                default: {
                    if (SERVER_MODE || window.top !== window) return;
                    switch (event.operation) {
                        case 'jscript':
                            event.$additional.forEach(i => {
                                if (i.value) {
                                    console.log('%coda-server', 'font: bold; color: white; background-color: blue; padding: 1px 8px;', i.value);
                                }
                            });
                            break;
                        case 'jscript-log':
                            event.$additional.forEach(i => {
                                if (i.value) {
                                    const color = i.toString().toLowerCase().startsWith('error') ? 'red' : 'silver';
                                    console.log('%coda-server', `font: bold; color: white; background-color: ${color}; padding: 1px 8px;`, i.value);

                                }
                            });
                            break;
                        case 'jscript-warn':
                            event.$additional.forEach(i => {
                                if (i.value) {
                                    console.warn('%coda-server', 'font: bold; color: white; background-color: orange; padding: 1px 8px;', i.value);
                                }
                            });
                            break;
                        case 'jscript-error':
                            event.$additional.forEach(i => {
                                if (i.value) {
                                    console.error('%coda-server', 'font: bold; color: white; background-color: red; padding: 1px 8px;', i.value);
                                }
                            });
                            break;
                    }
                }
            }
        },
        async onFilesChanged(e) {
            try { e = typeof e === 'string' ? JSON.parse(e) : e; }
            catch (e) { }
            const ownerFolder = await this.findItem(e.$owner.path);
            const target = await this.findItem(e.$F[0].path);
            if (target && e.operation === 'delete') {
                const broadCastDelete = async (item) => {
                    item.reset?.();
                    item.fire('delete');
                    if (item instanceof odaFolder) {
                        return Promise.all((await item.getFilesAndFolders()).map(broadCastDelete));
                    }
                };
                await broadCastDelete(target);
            }
            ownerFolder?.reset();
            ownerFolder?.attachmentsFrom?.reset();
            for (const k in this.__customCache__) {
                delete this.__customCache__[k];
            }
            this['#$$handlers'] = undefined;
            this['#$$history'] = undefined;
            this.$handlerSettingsStorage._handlerStructure = undefined;
            this.fire('changed');
        },
        getObject(arg) {
            switch (typeof arg) {
                case 'object': {
                    arg.id ??= arg.oid;
                    const storage = this.$base || this.$part;
                    arg.bid ??= storage.id;
                    arg.cid ??= this.id;
                    if (arg.cid === this.id && arg.bid === storage.id)
                        return odaObject.build(arg, this);
                    const path = `B:${arg.bid}/C:${arg.cid}`;
                    const key = 'get_object:' + path + ':' + arg.id;
                    return this.cache(key, async () => {
                        let cls = storage.findItem(path);
                        if (cls?.then) {
                            try {
                                cls = await cls;
                            }
                            catch (e) {
                                cls = this;
                            }
                        }
                        // if (!cls) {
                        //     cls = this;
                        //     console.warn(`The real class of the object was not found by path: "${path}"`);
                        // }
                        return this.__customCache__[key] = odaObject.build(arg, cls);
                    })
                } break;
                case 'string': {
                    return this.findItem(arg.includes('O:') ? arg : `O:${arg}`);
                } break;
            }
        },

        async addClass(prototype = {}, silent = false) {
            if (this.$base && ['id', 'Type', 'type', 'sys:is-virtual', 'sys:origin-base', 'ClassId'].every(k => !prototype[k]))
                prototype['sys:origin-base'] = this.$base.id;

            const body = { $CLASS: [prototype] };
            if (silent) {
                CORE.lastCreated = 'silent';
            }

            let res = await this.request('create_class', undefined, body);
            const cls = await this.findItem(res.result);
            return cls;
        },
        getInheritRoot() {
            return {
                ClassId: this.id,
                Name: this.name,
                Label: this.label
            };
        },
        async inheritClass(inheritItem, inheritChild = false, silent = false) {
            const item = await this.addClass(inheritItem.getInheritRoot(), silent);
            if (inheritChild) {
                const classes = await inheritItem.$$classes;
                for (const child of classes) await item.inheritClass(child, inheritChild, true);
            }
            item.save();
            inheritItem.fireServerEvent('inherited', this);
            return item;
        },
        async copyClass(copyItem, copyChild = false, silent = false, params = {}) {
            await copyItem.load();
            const body = Object.assign({}, copyItem.body, { ClassId: undefined }, params)
            const item = await this.addClass(body, silent);
            if (copyChild) {
                const classes = await copyItem.$$classes;
                for (const child of classes) await item.copyClass(child, copyChild, true);
            }
            item.save();
            //copyItem.fireServerEvent('copied', this);
            return item;
        },
        async _recalcObject(obj, metadata) {
            if (obj instanceof odaObject) {
                obj = { [obj.rootTag]: obj.body };
            }
            // Temporarily. To delete this and  the "async" function's prefix when it will by realized on the server-side: the object recalculating for workplace's class
            if (this.$owner instanceof odaWorkplace) {
                // const stat = await (await fetch(`/api${this.path}/O:${this.id}`)).json();
                const stat = await CORE.request(`/api${this.path}/O:${this.id}`);
                Object.assign(obj, stat);
            }
            ////
            return this.request('recalc', { meta: metadata }, ((typeof obj === 'object') ? JSON.stringify(obj) : obj));
        },
        fireServerEvent(name, data) {
            this.$host.fireServerEvent(this, name, data);
        },
        allowInherit(to) {
            if (to.access.level > 3 && to instanceof odaBase) {
                let item = to.$base;
                while (item) {
                    if (item === this.$base) {
                        return true;
                    }
                    item = item === item.$base ? item.$owner : item.$base;
                }
            }
            return false;
        },
        allowMove(to) {
            return to instanceof odaStorage && this.access.level >= 6 && to.access.level >= 3;
        },
        created() {
            this.eventHandler_ = this.eventHandler.bind(this);
            this.$host.addListen(this, this.eventHandler_);

            this.onFilesChanged_ = this.onFilesChanged.bind(this);
            this.$host.addCustomListen(this, 'files-changed', this.onFilesChanged_);
        }
    }) {
        static DEFAULT_ICON = 'odant:class'
    }
    //#endregion odaStorage

    INDEX: {
        //#region odaIndexDataSet
        globalThis.odaIndexDataSet = class odaIndexDataSet extends odaVirtualItem.ROCKS({
            $public: {
                mask: '*',
                filter: String,
            },
            get $storage() {
                return this.$owner.$storage;
            },
            get items() {
                return this.$owner.request('xquery_index', { loadmask: this.mask, format: 'json' }, '*/*').then(res => {
                    return (res?.$O || []).map(r => {
                        return this.$storage.getObject(r);
                    });
                })
            },
            get metadata() {
                return this.$owner.$$fields;
            },
            async getItems(from = 1, length = 100) {
                try {
                    const data = await this.$owner.request('xquery_index',
                        { loadmask: this.mask, format: 'json', async: true },
                        `subsequence(*/*, ${from}, ${length})`);
                    return data[Object.keys(data)[0]];
                } catch (e) {
                    console.error(e);
                    return [];
                }
            },
            async getRowItem(row) {
                if (row) {
                    let rows = Array.isArray(row) ? row : [row];
                    if (rows.length === 0)
                        return Promise.resolve(null);

                    const item = await this.$storage.getObject(rows[0]);
                    if (rows.length === 1)
                        return item;
                    return new odaGroup({ $owner: this.$storage, $item: item, items: rows.map(i => { return i.oid }) });
                }
                return Promise.reject();
            }
        }) {}
        //#endregion odaIndexDataSet

        //#region odaIndexOwner
        globalThis.odaIndexOwner = class odaIndexOwner extends odaItem.ROCKS({
            $$indexes: {
                $type: Array,
                get() {
                    return Promise.all(this.data['/INDEXES/INDEX'].map(i => odaIndex.build(i, this)));
                },
            },
            async createIndex({ id, label = '', type = '', xq = '' }) {
                const $indexes = this.data[`/INDEXES/INDEX`];
                $indexes.push({ id, label, type, $CDATA$: xq, $METADATA: [{}] });
                this.reset?.();
                this.fire('create-index', id);
                // this.isChanged = true;
            },
            async deleteIndex(index) {
                await this.load();
                const $indexes = this.data[`/INDEXES/INDEX`];
                const idx = $indexes.findIndex(i => i.id === index.id);
                if (~idx) $indexes.splice(idx, 1);
                this.reset?.();
                this.fire('delete-index', index.id);
                // this.isChanged = true;
            },
            async getIndex(id, XQ, type) {
                if(!this.body){
                    await this.load?.();
                }
                const $$indexes = await this.$$indexes;
                let index = $$indexes.find(i => i.id === id);
                if (index) {
                    return index;
                }
                return this._old_getIndex(id, XQ, type);
            },
            _old_getIndex(name, XQ, type) {
                return this.cache('Index:' + name, async () => {
                    if (XQ) {
                        const param = { name };
                        if (type) param.type = type;
                        await this.API.POST('create_index', param, XQ);
                    }
                    return odaIndex.build({ id: name, name }, this);
                });
            }
        }) {}
        //#endregion odaIndexOwner

        //#region odaIndex
        globalThis.odaIndex = class odaIndex extends odaServerItem.ROCKS({extends: [odaFieldOwner, odaIndexOwner],
            prefix: 'I',
            tag: 'INDEX',
            typeName: 'index',
            // $public: {
            //     moreSources: {
            //         $type: Array,
            //         get() {
            //             return this.data.$SOURCES || []
            //         },
            //         set(n) {
            //             this.data.$SOURCES = n;
            //         },
            //     }
            // },
            order: 10,
            get icon() {
                return 'carbon:table-of-contents';
            },
            // get path() {
            //     const $owner = this.$owner instanceof odaExtIndexes ? this.$owner.$owner : this.$owner;
            //     return `${$owner.path}/${this.prefix}:${this.id}`;
            // },
            get hasChildren() {
                return !this.isSystem;
            },
            get isSystem() {
                return this.data.isSystem ?? false;
            },
            get isChanged() {
                return this.$owner.isChanged;
            },
            set isChanged(v) {
                this.$owner.isChanged = v;
            },
            get $index() {
                return this;
            },
            get $object() {
                return this.$owner.$object;
            },
            get $storage() {
                return this.$owner.$storage;
            },
            packs: {
                $type: Array,
                get() {
                    return this.$storage.data.$Pack;
                }
            },
            get $$packets() {
                const count = this.changePackets;
                return (async () => {
                    const json = await this.request('get_pack_list');
                    return json.$FOLDER[0].$FILE ? json.$FOLDER[0].$FILE.reduce((res, v) => {
                        const names = v.Name.split('#');
                        const name = names.shift();
                        let p = res.find(i => i.name === name);
                        if (!p) {
                            p = { name: name, count: 0, items: [] };
                            res.push(p);
                        }
                        p.count += +v.Count;
                        if (names.length)
                            p.items.push({ count: v.Count, names: names });
                        return res;
                    }, []).map(i => {
                        return new odaPack(i, this);
                    }) : [];
                })();
            },
            $$fields: {
                $type: Array,
                get() {
                    // if (this.isSystem) return [];

                    return [this.$FIELDS];
                }
            },
            get $$sources() {
                return (async () => {
                    if (!this.body) {
                        await this.load();
                    }
                    const isChanged = this.isChanged;
                    const sources = this.data.$SOURCE ??= [];
                    this.isChanged = isChanged;
                    return this.$$sources = sources;
                })()
            },
            get $$relations() {
                return this.load().then(async res => {
                    const isChanged = this.isChanged;
                    const srcs = this.data.$RELATION ??= [];
                    this.isChanged = isChanged;
                    return this.$$relations = srcs;
                });
            },
            get xQuery() {
                return (async () => {
                    const xq = `
for $a in //PACK/OBJECT group by $a/@oid
return
    element O {
${(await this.$FIELDS.$$fields).reduce((res, f, index) => {
                        res += `\
        attribute ${f.name} { $a${f.valueExpr} },\n`
                        return res;
                    }, '').slice(0, -2)}
    }
`;
                    return this.xQuery = xq;
                })()
            },
            async addSource(path) {
                if (!this.body) await this.load();
                if (path !== this.$storage.path && !this.data.$SOURCE.some(s => s.path === path)) {
                    this.data.$SOURCE.push({ path });
                    this.$$sources = undefined;
                }
            },
            async removeSource(path) {
                if (!this.body) await this.load();
                const idx = this.data.$SOURCE.findIndex(s => s.path === path);
                if (~idx) {
                    this.data.$SOURCE.splice(idx, 1);
                    this.$$sources = undefined;
                }

            },
            async addRelation({ from, to }) {
                if (!this.body) await this.load();
                this.data.$RELATION ??= [];
                const id = Math.random().toString().slice(2, 2 + 15);
                const relation = {
                    source: this.getRelativePath(from.$bodyOwner),
                    from: from.xPath,
                    target: this.getRelativePath(to.$bodyOwner),
                    to: to.xPath
                }

                if (this.data.$RELATION.every(s => !Object.equal(s, relation, true))) {
                    this.data.$RELATION.push(relation);
                    this.$$relations = undefined;
                }
            },
            async removeRelation(id) {
                if (!this.body) await this.load();
                const idx = this.data.$RELATION?.findIndex?.(s => s.id === id);
                if (~idx) {
                    this.data.$RELATION.splice(idx, 1);
                    this.$$relations = undefined;
                }

            },
            created() {
                this.$host.addListen(this, () => this.fire('changed'));
            },
            load() {
                this.isChanged;
                return this.data;
            },
            reset() {
                this['#$$indexes'] = undefined;
                this.resetCache('$$indexes');
                if (this instanceof odaExtIndexes) {
                    this.$owner['#$CUSTOM'] = undefined;
                    this.$owner['#$$indexes'] = undefined;
                    this.$owner.fire('changed');
                }
                else {
                    this.$owner.reset?.();
                }
                this.fire('changed');
            },
            getRows(mask, from, length, post) {
                if (typeof post === 'object')
                    post = JSON.stringify(post);
                return this.request('dataset', { loadmask: mask, mask: mask, from: from, length: length }, post);
            },
            getDataSet(mask = '*', filter, clear) {
                if (clear) this.reset();
                return this.cache('DataSet-' + mask + filter, () => {
                    return new odaIndexDataSet({ $index: this, mask, filter }, this);
                }, 100000) //todo Предложение по сбросу кэша
            },
            getCascade(name, XQ) {
                return this.getIndex(name, XQ, 'c');
            },
            save() {
                this.$owner.save?.();
            },
            delete() {
                this.$owner.deleteIndex(this);
                this.fire('delete');
            }
        }) {
            static defaultView = 'object-table';
        }
        //#endregion odaIndex

        //#region odaExtIndexes
        globalThis.odaExtIndexes = class odaExtIndexes extends odaIndex.ROCKS({
            tag: 'INDEXES',
            isSystem: true,
            $$fields: [],
            hasChildren: true,
            get label() {
                return `[${this.id.toUpperCase()}]`
            },
        }) {}
        //#endregion odaExtIndexes
    }

    //#region odaClass
    globalThis.odaClass = class odaClass extends odaStorage.ROCKS({extends: [odaIndexOwner],
        $typeClass: {
            $type: Object,
            get() {
                return this.$types?.[0];
            }
        },
        $types: {
            $type: Array,
            get() {
                let classes = this.type?.map(type => {
                    return type.$typeClass;
                })
                return Promise.all(classes).then(res => {
                    return res?.filter(Boolean);
                })
            }
        },
        // для ссылок на класс в рабочем месте
        $source: {
            $type: Array,
            get() {
                if (!this.draft.source) return null;
                return this.findItem(this.draft.source);
            }
        },
        prefix: 'C',
        changePackets: 0,
        isVirtual: {
            $type: Boolean,
            get() {
                return this.draft?.['sys:is-virtual'];
            }
        },
        get $rootClass() {
            return (this.$owner instanceof odaClass) ? this.$owner?.$rootClass : this;
        },
        get instructionForAddItem() {
            if (this.$module) {
                if (this.developmentMode)
                    return {
                        class: ['implement new in base', 'create child', 'upgrade existing']
                    }
                return {
                    class: ['implement new in base', 'upgrade existing']
                }
            }
            else if (this.isVirtual) {
                return {
                    class: ['virtual']
                }
            }
            return {
                class: ['create child', 'inherit', 'virtual']
            }
        },
        get color() {
            if (this.$module)
                return `var(--item-module-class-color)`;
            return `var(--item-${this.typeName}-color)`;
        },
        get $class() {
            return this;
        },
        // category: {
        //     // $type: String,
        //     get() {
        //         return this.draft.category;
        //     }
        // },
        $public: {
            type: {
                $editor: '@lib/type-editor[odant-type-array-editor]',
                $type: Array,
                get() {
                    return (this.data.type || '').split('\n').filter(i => i).map(i => {
                        return new odaMoreType(i, this, 'type');
                    })
                },
                async set(v) {
                    this.data.type = this.data.Type = v.map(i => i.path).filter(i => i).join('\n');
                },
            },
            pack: {
                $type: String,
                $editor: '@lib/pack-editor[odant-pack-editor]',
                get() {
                    return this.data.Pack;
                },
                set(n) {
                    this.data.Pack = n;
                },
            },
            packs: {
                $type: Array,
                $editor: '@lib/packs-editor[odant-packs-editor]',
                get() {
                    if (this.data.$PACKS?.length && this.data.$PACKS[0].$PACK?.length) {
                        return this.data.$PACKS[0].$PACK.map(i => new odaMorePack(i, this));
                    }
                },
                set(n) {
                    this.data.$PACKS = n;
                },
            },
            isAbstract: {
                $type: Boolean,
                get() {
                    return this.draft.abstract ?? this.draft.Abstract ?? false;
                },
                set(n) {
                    this.data.abstract = this.data.Abstract = n;
                }
            },
            defaultView: {
                $type: String,
                $group: 'Controls',
                $def() {
                    return this.getDefHandler('view');
                },
                get() {
                    let view = this.getDefHandler('view');
                    if (this.isAbstract) {
                        if (this.access.isAdmin)
                            view = 'configurator';
                        else
                            view = 'object-table';
                    }
                    return this.data['default-view'] || view;
                },
                set(n) {
                    let view = this.getDefHandler('view');
                    if (this.isAbstract) {
                        if (this.access.isAdmin)
                            view = 'configurator';
                        else
                            view = 'object-table';
                    }
                    if (n === view)
                        delete this.data['default-view'];
                    else
                        this.data['default-view'] = n;
                }
            },
            defaultObjectView: {
                $type: String,
                $group: 'Objects',
                $editor: '@lib/select-handler-editor[odant-select-object-view-editor]',
                get() {
                    return this.getDefHandler('objectView');
                },
                set(n) {
                    this.setDefHandler('objectView', n);
                },
            },
            objectNameExpression: {
                $type: String,
                $group: 'Expressions',
                $editor: '@lib/expression-editor[odant-expression-editor]',
                get() {
                    return this.data.XName;
                },
                set(n) {
                    this.data.XName = n;
                },
            },
            interbaseDataMode: {
                $type: String,
                $group: 'Objects',
                $list: ['None', 'Aggregation', 'Inheritance', 'Mixed'],
                get() {
                    return this.data['InterbaseDataMode'] || 'Aggregation';
                },
                set(n) {
                    this.data['InterbaseDataMode'] = n
                }
            },
            filter: {
                $type: String,
                $group: 'Expressions',
                get() {
                    return this.data['Filter'];
                },
                set(n) {
                    this.data['Filter'] = n
                }
            }
        },
        get subIcon() {
            return this.isVirtual ? 'bootstrap:eye' : '';
        },
        get _fieldViews() {
            return {
                'field-input': [],
                'field-control': [],
                'field-toolbar': [],
                'field-table': []
            }
        },
        get isLink() {
            return (this.draft.link === "True");
        },

        get $workplace() {
            return this.$owner?.$workplace;
        },
        typeName: 'class',
        get typeLabel() { //todo: system-class
            return this.isLink
                ? this.data.source
                    ? 'link class'
                    : 'lost link class'
                : this.$module
                    ? 'module class'
                    : 'class'
        },
        get order() {
            let order = 10;
            if (this.isAbstract) {
                order += 2;
            }
            else if (!this.inherit?.isLink) {
                order += 1;
            }

            if (!this.$module) {
                order += 2;
            }
            return order;
        },
        get $topClass() {
            return this.$owner.prefix === 'C' ? this.$owner.$topClass : this;
        },
        get hasObjects() {
            return !this.hasChildren;
        },
        // 👀
        get hasChildren() {
            const hasChildren = this.$super('hasChildren');
            if (!hasChildren && this.$module) {
                const $$typed = this.$$typed;
                if ($$typed instanceof Promise)
                    return this.$$typed.then($$typed => {
                        return this.hasChildren = ($$typed?.length > 0);
                    });
                return ($$typed?.length > 0);
            }
            return hasChildren;
        },
        get $$typed() {
            return this.$base?.getTypedBy(this.id);
        },
        $$objects: {
            $type: Array,
            get() {
                if (!this.hasChildren)
                    return this.getIndex('table').then(async idx => {
                        let res = idx.$$packets;
                        const ds = await idx.getDataSet();
                        let items = await ds.items;
                        items = await Promise.all(items)
                        // const objects =  items.map(i=>{
                        //     return odaObject.build(i, this);
                        // });
                        return items;
                    })
                return [];
            }
        },
        get $$packets() {
            const count = this.changePackets;
            return (async () => {
                const json = await this.request('get_pack_list');
                return json.$FOLDER[0].$FILE ? json.$FOLDER[0].$FILE.reduce((res, v) => {
                    const names = v.Name.split('#');
                    const name = names.shift();
                    let p = res.find(i => i.name === name);
                    if (!p) {
                        p = { name: name, count: 0, items: [] };
                        res.push(p);
                    }
                    p.count += +v.Count;
                    if (names.length)
                        p.items.push({ count: v.Count, names: names });
                    return res;
                }, []).map(i => {
                    return new odaPack(i, this);
                }) : [];
            })();
        },
        get HANDLERS() {
            return this.getRootElement('HANDLERS');
        },
        count: {
            $def: 0,
            get() {
                return this.getObjectCount();
            }
        },
        hasInheritClasses: {
            $def: 0,
            get() {
                return this.getInheritClassPaths().then(list => {
                    return list.length;
                });
            }
        },
        // findItem(path, params) {
        //     // todo: убрать костыль. Добиться, чтобы работал поиск объекта от класса
        //     if (path?.includes('O:')) return (this.$base || this.$part).findItem.call(this, path, params);
        //     return (this.$base || this.$part).findItem(path, params);
        // },
        checkIn($item) {
            if (!this.$part.isDataStorage || this.$workplace) return [];

            const methods = [];
            switch ($item.prefix) {
                case 'C': {
                    if (!$item.path.includes('W:')) {
                        if ($item.$base.contains(this.$base)) {
                            methods.add('inherit');
                        } else {
                            methods.add('move');
                        }
                        if (this.$owner.prefix === 'W') {
                            methods.add('link');
                        } else if ($item.$module && !this.$owner.$module) {
                            methods.add('implement');
                        }
                    }
                }
            }
            return methods;
        },
        getRootElement(name) {
            return this.data?.[`$${name}`]?.[0];
        },
        async getObjectCount(pack) {
            return this.cache('object-count', async () => {
                const json = await this.request('get_class_objects_count', { format: 'json', mask: pack || '*' });
                return json && parseInt(json.result) || 0;
            });
        },
        async createObject(src) {
            if (src && typeof src === 'object')
                src = JSON.stringify(src);
            const json = await this.request('create_object', { recalc: true, meta: true }, src);
            // TODO load extended object script
            const data = (json.$OBJECT || json.$STATIC || json.$DETAIL || [{}])[0];
            const obj = await odaObject.build(data, this);
            obj.body = data;
            obj.draft = undefined;
            obj.isChanged = true;
            obj.isNew = true;
            let cls;
            do {
                cls = cls?.$owner || this;
                cls.fire('created', obj);
            }
            while (this.$topClass && (cls.id !== this.$topClass.id));
            return obj;
        },
        async getInheritClassPaths() {
            const xq = `\
declare function local:func($node as node()*) as node()*{
    for $b in $node/B
    return
        if ($b[C[@id='${this.id}']])
        then
            element i {
                attribute p { string-join(reverse($b/ancestor-or-self::*/concat(name(),':',@id)), '/') }
            }
        else local:func($b)
};

local:func(B)`;
            const context = this.$base
            if (!context) return [];
            const list = (await context.request('config', { prefix: 'true', format: 'json-2' }, xq))?.i || [];
            return list.map(i => `${i.p}/C:${this.id}`);
        },
        // createIndex({id, xq}) {
        //     return this.request('create_index', { name: id }, xq);
        // },
    }) {
        static defaultView = 'object-table';
        static defaultPage = 'navigator';
        static defaultForm = 'form';
        static defaultObjectView = 'data-editor';
    }
    //#endregion odaClass

    //#region odaPack
    globalThis.odaPack = class odaPack extends CORE({
        icon: 'av:library-books',
        $public: {
            name: String,
            items: Array,
        },
        get hasPacks() {
            return this.$$packets.length > 0;
        },
        get $$packets() {
            return this.items.reduce((res, v) => {
                const names = v.names;
                const name = names.shift();
                let p = res.find(i => i.name === name);
                if (!p) {
                    p = { name: name, count: 0, items: [] };
                    res.push(p);
                }
                p.count += +v.count;
                if (names.length)
                    p.items.push({ count: v.count, names: names });
                return res;
            }, []).map(i => {
                return new odaPack(i, this)
            });
        }
    }) {
        constructor(props = {}, owner) {
            super(...arguments);
            for (const n in props) {
                this[n] = props[n];
            }
            this.$owner = owner;
        }
    }
    //#endregion odaPack

    //#region odaGroup
    globalThis.odaGroup = class odaGroup extends odaVirtualItem.ROCKS({
        icon: 'odant:folder',
        isRef: false,
        hasChildren: true,
        typeName: 'group',
        get instructionForAddItem() {
            return {
                [this.$item.typeName]: 'create',
            }
        },
        get count() {
            return this.$$items?.length;
        },
        prefix: 'GRP',
        get order() {
            return this.data.order ?? ((this.$item?.order || 0) + 5);
        },
        get path() {
            // todo: для сравнения групп (понимания, отличаются или нет)
            return this.$owner.path + '[' + (this.$$items?.map?.(i => i.id).join(',') || '') + ']';
        },
        get $$items() {
            return this.data.items;
        },
        get $$classes() {
            return this.$$items.filter(i => i.prefix === 'C');
        },
        get $item() {
            return this.$$items[0];
        },
        get id() {
            return this.data.id || this.$item?.category || this.$item?.typeName;
        },
        get label() {
            return this.id;
        },
        get subIcon() {
            return this.data.subIcon;
        },
        get color() {
            return this.data.color || this.$item?.color;
        },
        get fill() {
            return this.color;
        },
        get doInherit() {
            return this.$owner.doInherit;
        },
        get doMove() {
            return this.$owner.doMove;
        },
        get allowInherit() {
            return this.$owner.allowInherit;
        },
        get allowMove() {
            return this.$owner.allowMove;
        },
        get $$handlers() {
            const filterHandlers = async (res) => {
                const actions = res.filter(a => a.allowArrayContext);
                const prom = actions.map(h => {
                    return h.constructor.build(h.draft, this);
                });
                return (await Promise.all(prom)).filter(Boolean);
            }
            if (this.$item.$$handlers.then)
                return this.$item.$$handlers.then(filterHandlers);
            return filterHandlers(this.$item.$$handlers);
        },
        get isSystem() {
            return this.$item && ['isSystem', 'isSystemField'].some(p => this.$item[p]);
        },
        get hide() {
            return this.data.hide === 'True' || !this.$$items || this.$$items.length === 0 || this.$$items.every?.(i => i.hide);
        },
        delete() {
            return Promise.all(this.$$items.map(item => item.delete()));
        },
        import(...args) {
            this.$item?.import(...args);
        },
        getHandlerCtor(type, ext, path) {
            // 👀
            type = this.$item.typeName;
            return (this._itemCtors[`${type}${ext || ''}${path}`] ??= this.$owner?.getHandlerCtor(type, ext, path));
        },
    }) {}
    //#endregion odaGroup

    //#region odaDomain
    globalThis.odaDomain = class odaDomain extends odaStorage.ROCKS({
        get $module() {
            return null;
        },
        $$fields: [],
        count: 0,
        prefix: 'D',
        typeName: 'domain',
        type: {
            $type: String,
        },
        get icon() {
            return this.isSystem ? 'icons:lock' : 'odant:domain';
        },
        get order() {
            return this.inherit?.isLink ? 61 : 60;
        },
        get isSystem() {
            return (this.id === '000000000000000');
        },
        get $domain() {
            return this;
        },
        get $$users() {
            return getUsers.call(this);
        },
        get $$assignedUsers() {
            return this.$$users.then($$users => $$users.filter(u => u.assigned));
        },
        get $$unassignedUsers() {
            return this.$$users.then($$users => $$users.filter(u => !u.assigned));
        },
        async _createDomain(prototype, prefix = 'D', silent = false) {
            if (prefix !== 'B' && this.$base && ['id', 'sys:origin-base', 'ClassId'].every(k => !prototype[k])) {
                prototype['sys:origin-base'] = this.$base.id;
            }
            prefix = prefix.toUpperCase();
            prototype.Type = prototype.Type || prefixMap[prefix]?.toUpperCase();
            prefix = prototype.Type[0]
            const body = { $CLASS: [prototype] };
            if (silent) {
                CORE.lastCreated = 'silent';
            }
            const res = await this.request('create_domain', {}, body);
            const cls = await this.getItem(`${prefix}:${res.id || res[`$${prefix}`]?.[0]?.id}`);
            return cls;
        },
        getInheritRoot() {
            return {
                ClassId: this.id,
                Name: this.name,
                Label: this.label,
                Type: this.type
            };
        },
        async inheritDomain(inheritItem, inheritChild = false, silent = false, inheritNoAbstractClassesOnly = false) {
            const item = await this._createDomain(inheritItem.getInheritRoot(), inheritItem.prefix, silent);
            await item.load();
            if (inheritChild) {
                const domains = await inheritItem.getItems(inheritItem.prefix);
                for (const child of domains) await item.inheritDomain(child, inheritChild, true);

                if (inheritItem.prefix != 'W') {
                    const classes = await inheritItem.$$classes;
                    for (const child of classes) {
                        if(!inheritNoAbstractClassesOnly || !child.isAbstract){
                            await item.inheritClass(child, inheritChild, true);
                        }
                    }
                }
            }

            if (!silent)
                this.fire('update');
            return item;
        },
        async copyDomain(copyItem, copyChild = false, silent = false, params = {}) {
            await copyItem.load();
            const body = Object.assign({}, copyItem.body, { ClassId: undefined }, params)
            const item = await this._createDomain(body, copyItem.prefix, silent);
            await item.load();
            if (copyChild) {
                const domains = [...(await copyItem.$$workplaces || []), ...(await copyItem.$$modules || [])];
                for (const child of domains) await item.copyDomain(child, copyChild, true);

                if (copyItem.prefix != 'W') {
                    const classes = await copyItem.$$classes;
                    for (const child of classes) await item.copyClass(child, copyChild, true);
                }
            }

            if (!silent)
                this.fire('update');
            return item;
        },
        async assignUsers(ids) {
            const body = ids.join(' ');
            if (body) {
                const res = await this.request('add_users', {}, body);
                this.$$users = undefined;
                this.$$assignedUsers = undefined;
                this.$$unassignedUsers = undefined;
                this.fire('changed')
                return res;
            }
        },
        async unassignUsers(ids) {
            const body = ids.join(' ');
            if (body) {
                const res = await this.request('remove_users', {}, body);
                this.$$users = undefined;
                this.$$assignedUsers = undefined;
                this.$$unassignedUsers = undefined;
                this.fire('changed')
                return res;
            }
        },
    }) {
        static defaultView = 'desktop';
        static defaultForm = 'form';
    }
    //#endregion odaDomain

    //#region odaPart
    globalThis.odaPart = class odaPart extends odaDomain.ROCKS({extends: [odaWorkplaceOwner, odaModuleOwner],
        get instructionForAddItem() {
            if (this.id === 'WORK')
                return {
                    base: ['create'],
                }
            return {};
        },
        isSystem: true,
        prefix: 'P',
        typeName: 'part',
        $public: {
            category: {
                get() {
                    return '';
                }
            },
        },
        get checkAdd() {
            if (this.isDataStorage)
                return ['base'];
            return [];
        },
        get icon() {
            switch (this.id.toLowerCase()) {
                case 'public':
                    return 'odant:root';
                case 'root':
                    return 'fontawesome:s-gears';
                case 'work':
                    return 'odant:structure';
            }
            return this.id.toLowerCase();
        },
        get isDataStorage() {
            return (this.id.toLowerCase() === 'work' || this.id.toLowerCase() === 'users');
        },
        get $base() { return null; },
        get $part() { return this; },
        get $$bases() {
            return this.getItems('B');
        },
        get order() {
            switch (this.id.toLowerCase()) {
                case 'public':
                    return 57;
                case 'root':
                    return 58;
                case 'work':
                    return 59;
            }
            return 60;
        },
        addBase(source) {
            return this._createDomain(source, 'B');
        },
        checkIn($item) {
            if (!this.isDataStorage || $item.prefix !== 'B')
                return false;
            return $item.checkOut(this);
        },
        checkOut($item) {
            return false;
        },
        created() {
            this.eventHandler_ = this.eventHandler.bind(this);
            this.$host.addListen(this, this.eventHandler_);

            this.onFilesChanged_ = this.onFilesChanged.bind(this);
            this.$host.addCustomListen(this, 'files-changed', this.onFilesChanged_);
        },
    }) {
        constructor(draft, owner) {
            // if(draft.id === 'WORK')
            //     owner = owner.$root;
            super(draft, owner)
            if (this.constructor.prototype.typeName === 'part' && this.id !== 'ROOT' && this.id !== 'PUBLIC' && this.id !== 'SYSTEM') {
                Promise.resolve(this.$host.$root).then(() => {
                    if (this.$host.$root !== this)
                        this.$owner = this.$host.$root;
                });
            }
        }
    }
    //#endregion odaPart

    //#region odaBase
    globalThis.odaBase = class odaBase extends odaPart.ROCKS({extends: [odaWorkplaceOwner, odaModuleOwner],
        developmentMode: false,
        async getTypedBy(type) {
            const xq = `\
declare function local:get-typed( $node as node(), $path as xs:string? )
        as xs:string? {
    if (($node/name() = 'C') and $node/ends-with(@type, '${type}') and ($node/@id != '${type}'))
    then
        concat($path, '/', $node/name(), ':', $node/@id)
    else
        string-join(
            for $e in $node/(M, W, C)
            return local:get-typed($e, concat($path, '/', $node/name(), ':', $node/@id)),
        ' ')
};

normalize-space(
    string-join(
        for $e in B/(M, W, C)
        return local:get-typed($e, ''),
    ' ')
)`;

            // const xq = 'for $e in B/(C, C//C) return $e/@id';

            return this.request('config', { prefix: true, format: 'text' }, xq).then(async res => {
                const prom = res?.split(' ')?.map(async path => {
                    return this.findItem(path.slice(1));
                }) || [];
                return Promise.all(prom);
            })
        },
        prefix: 'B',
        typeName: 'base',
        isSystem: false,
        type: {
            $type: String,
        },
        $public: {
            // category: 'Bases',
            defaultView: {
                $def: null,
                get() {
                    return this.hasChildren || !this.access.isAdmin ? 'desktop' : this.hasFields ? 'static' : 'configurator';
                },
                set: null
            },
            icon: {
                get() {
                    return this.draft.icon || 'odant:base';
                },
            },
        },
        instructionForAddItem: {
            class: ['create', 'inherit'],
            base: ['create', 'copy'],
            workplace: ['create', 'inherit', 'install'],
            module: ['create', 'inherit', 'install']
        },
        checkAdd: ['class', 'base', 'workplace', 'module'],
        get order() {
            return this.inherit?.isLink ? 71 : 70;
        },
        get $base() { return this; },
        get $part() { return this.$owner.$part; },
        get $system() { return this.getItem('D:000000000000000') },
        get $$systems() {
            if (this.$system instanceof Promise)
                return this.$system.then(s => [s]);
            return [this.$system];
        },
        get hasFields() {
            return this.$STATIC.hasFields;
        },
        get $STATIC() {
            return new odaDetailsFields(this.data?.$STATIC?.[0], this)
        },
        get $$fields() {
            return [this.$STATIC];
        },
        get count() {
            return this.draft.$B?.length || 0;
        },
        checkIn($item) {
            if (!this.$part.isDataStorage) return false;

            const methods = [];
            switch ($item.prefix) {
                case ('C'): {
                    if ($item.$base?.contains?.(this)) methods.add('inherit');
                    else methods.add('move');
                    methods.add('copy');
                    if (!this.containsLink($item)) methods.add('link');
                } break;
                case ('B'): {
                    if ($item.$host.id === this.$host.id) methods.add('copy');
                    if (!this.containsLink($item)) methods.add('link');
                } break;
                case ('W'): {
                    if (!$item.$base?.contains?.(this)) methods.add('move');
                    if (!this.contains($item)) methods.add('inherit');
                    if (!this.containsLink($item)) methods.add('link');
                } break;
                case ('M'): {
                    if (!this.contains($item)) methods.add('install');
                    //if (!this.containsLink($item)) methods.add('link');
                } break;
            }
            return methods;
        },
        checkOut($item) {
            return $item.checkIn(this);
        },
        containsLink(item) {
            if (this.config?.[`$${item.prefix}`]?.find?.(i => i.id === item.id))
                return true;
            const links = this.data['/LINKS/LINK'];
            for (const i in links) {
                const l = links[i];
                const link = l['sys:link'] || l['link'];
                const j = link.lastIndexOf(':');
                if (link.substr(j - 1) === `${item.prefix}:${item.id}`) {
                    return true;
                }
            }
            return false;
        },
        HANDLERS: {
            $group: 'handlers',
            get _exportHandlersMap() {
                return this.$$modules?.then(async modules => {
                    return modules.reduce(async (res, module) => {
                        res = await res;
                        const config = await module.exportConfig;
                        if (Object.keys(config).length) {
                            res.set(module, config);
                        }
                        return res;
                    }, new Map())
                })
            },
        },
        DRAG_DROP: {
            $group: 'drag&drop',
            allowMove(to) {
                return to instanceof odaBase && this.access.level >= 6 && to.access.level >= 3;
            },
        },
        // todo: убрать, когда Аркадий начнёт выдавать ссылки на базы, модули, раб.места
        async getItems(prefix) {
            if (!this.hasChildren) return [];
            try {
                const config = await this.config;
                if (config) {
                    // реальные item из config
                    const items = config['$' + prefix] || [];

                    // ссылки на item
                    if (!this.body) await this.load();

                    const viewedLinks = [];
                    const prom = this.data['/LINKS/LINK']?.map(async l => {
                        const link = l['sys:link'] || l['link'];
                        if (viewedLinks.includes(link)) return;
                        viewedLinks.push(link);
                        const i = link.lastIndexOf(':');
                        const id = link.substr(i + 1);
                        if ((link.substr(i - 1, 1) === prefix) && !(~items.findIndex(c => (c.id === id)))) {
                            const realItem = await this.findItem(link);
                            if (realItem) {
                                let linkItem;
                                const draft = Object.assign({ id: realItem.id }, realItem.draft);
                                draft.$realItem = realItem;
                                switch (prefix) {
                                    case 'C': linkItem = new odaClassLink(draft, this); break;
                                    case 'B': linkItem = new odaBaseLink(draft, this); break;
                                    case 'M': linkItem = new odaModuleLink(draft, this); break;
                                    case 'W': linkItem = new odaWorkplaceLink(draft, this); break;
                                    default: return;
                                }
                                return linkItem;
                            }
                        }
                    });
                    const links = prom ? await Promise.all(prom) : [];

                    // смесь
                    return [...(await Promise.all(items.map(i => this.getItem(prefix + ':' + i.id)))), ...links.filter(Boolean)];
                }
            }
            catch (e) {
                console.error(e)
            }
        },
        async createLink(item) {
            this.data.$LINKS ??= [{}];
            this.data.$LINKS[0].$LINK ??= []
            this.data.$LINKS[0].$LINK.push({ id: item.id, link: item.path });
            this.save();
        },
    }) {}
    //#endregion odaBase

    //#region odaModule
    globalThis.odaModule = class odaModule extends odaDomain.ROCKS({extends: [odaModuleOwner],
        prefix: 'M',
        typeName: 'module',
        get subIcon() {
            return this.developmentMode ? 'bootstrap:tools' : '';
        },
        HANDLERS: {
            $group: 'handlers',
            _exportHandlersMap: {
                $type: Array,
                get() {
                    return this.$$classes?.then(async classes => {
                        const value = await classes?.reduce(async (res, cls) => {
                            const filterHandlers = handler => {
                                if (!handler.inherit || handler.inherit === 'parent' || handler.inherit === 'self') {
                                    handler.id = handler.name + '-' + cls.id;
                                    handler.sourceLabel = cls.label;
                                    handler.export = true;
                                    return true;
                                }
                            }
                            res = await res;
                            const map = await cls._handlerMap;
                            if (!map)
                                return res;
                            const value = map.get(cls)
                            Object.keys(value).forEach(side => {
                                const sideHandlers = value[side]?.[0] || {};
                                Object.keys(sideHandlers).forEach(type => {
                                    const typeHandlers = sideHandlers[type]?.[0] || {};
                                    Object.keys(typeHandlers).forEach(prop => {
                                        if (prop === 'H') {
                                            const propValue = typeHandlers[prop];
                                            const filterValue = propValue?.filter(filterHandlers) || []
                                            if (filterValue?.length) {
                                                res[side] ??= [{}];
                                                res[side][0][type] ??= [{}]
                                                res[side][0][type][0][prop] ??= [];
                                                res[side][0][type][0][prop].push(...filterValue);
                                            }
                                        } else {
                                            // file extensions
                                            const propValue = typeHandlers[prop]?.[0] || {};
                                            const filterValue = propValue?.H?.filter(filterHandlers) || []
                                            if (filterValue?.length) {
                                                res[side] ??= [{}];
                                                res[side][0][type] ??= [{}]
                                                res[side][0][type][0][prop] ??= [{ H: [] }];
                                                res[side][0][type][0][prop][0].H.push(...filterValue);
                                            }
                                        }
                                    });
                                });
                            });
                            return res;
                        }, {});
                        const map = new Map();
                        if (value)
                            map.set(this, value);
                        return map;
                    })
                }
            }
        },
        $public: {
            category: {
                get() {
                    return 'MODULES'
                }
            },
            icon: {
                get() {
                    return this.draft.icon || 'odant:module';
                },
            }
        },
        get order() {
            return this.inherit?.isLink ? 31 : 30;
        },
        get $module() { return this; },
        get instructionForAddItem() {
            if (this.developmentMode)
                return {
                    class: ['create', 'inherit']
                }
            else
                return {};
        },
        get checkAdd() {
            return this.developmentMode ? ['class'] : [];
        },
        checkIn($item) {
            // return [];
            if (!this.developmentMode) return [];
            const methods = [];
            switch ($item.prefix) {
                case 'C': {
                    //if (this.contains($item))
                        methods.add('move');
                    //methods.add('copy');
                } break;
            }
            return methods;
        },
        hasChildren: {
            $type: Boolean,
            get() {
                if (this.draft.childs === 'True')
                    return true;
                if (this.$part.id === 'ROOT')
                    return false;

                if (this.$parent?.then)
                    return this.$parent?.then(parent => parent?.hasChildren);
                return this.$parent?.hasChildren;
            }
        },
        getInheritRoot() {
            const root = this.$super('getInheritRoot');
            root['sys:last-update'] = this.data['sys:last-update'];
            return root;
        },
        async getItems(prefix) {
            if (!this.hasChildren && (this.$part.id === 'ROOT')) return [];
            try {
                const config = await this.config;
                if (config) {
                    // реальные item из config
                    const drafts = config['$' + prefix] || [];
                    const items = await Promise.all(drafts.map(i => this.getItem(prefix + ':' + i.id)));

                    if ((this.$part.id === 'ROOT')
                            || (this.$part.id === 'PUBLIC')
                            || this.developmentMode)
                        return items;

                    const root = await this.$host.$root;
                    const parent = await root.getItem(`${this.prefix}:${this.id}`);
                    //const parent = await this.$parent;
                    if (parent) {
                        // ссылки из parent в root
                        const links = [];
                        const parentItems = await parent.getItems(prefix);
                        for (let i = 0; i < parentItems.length; i++) {
                            const pItem = parentItems[i];
                            if (!~items.findIndex((item) => (item.id === pItem.id))) {
                                const draft = Object.assign({ id: pItem.id }, pItem.draft);
                                draft.$realItem = pItem;
                                const linkItem = new odaClassLink(draft, this);
                                links.push(linkItem);
                            }
                        }
                        // смесь
                        return [...items, ...links];
                    } else {
                        return items
                    }
                }
            }
            catch (e) {
                console.error(e)
            }
        }
    }) {}

    globalThis.odaWorkplace = class odaWorkplace extends odaDomain.ROCKS({extends: [odaWorkplaceOwner],
        prefix: 'W',
        typeName: 'workplace',
        canTake: ['class'],
        $public: {
            icon: {
                get() {
                    return this.draft.icon || 'odant:workplace';
                },
            },
        },
        instructionForAddItem: {
            class: ['create', 'inherit', 'link'],
            workplace: ['create', 'inherit']
        },
        checkAdd: ['class'],
        get order() {
            return this.inherit?.isLink ? 0 : 0;
        },
        get $workplace() {
            return this;
        },
        get $$workplaces() {
            return this.getItems('W');
        },
        getRelativePath(item) {
            const domain = this.$base ?? this.$part;
            return domain.getRelativePath(item);
        },
        async setAccessLevel(links = []) {
            return this.request('set_access_level', {}, { $O: links });
        },
        async createLink(item) {
            return await this.request('create_link', { classId: item.id });
        },
        checkIn($item) {
            const methods = [];
            switch ($item.prefix) {
                case 'C': {
                    if (!this.contains($item) && this.$base === $item.$base)
                        methods.add('link');
                    break;
                }
            }
            return methods;
        },
        async addClass(...p) {
            const cls = await this.$base.addClass(...p);
            return this.createLink(cls);
        },
    }) {}
    //#endregion odaWorkplace

    LINK: {
        // todo: убрать, когда Аркадий начнёт выдавать ссылки на базы, модули, раб.места
        //#region odaStorageLink
        globalThis.odaClassLink = class odaClassLink extends odaClass.ROCKS({
            typeLabel: 'class link',
            get isLink() {
                return true;
            },
            get $realItem() {
                return this.draft.$realItem;
            },
            get url() {
                return this.$realItem.url;
            },
            get path() {
                return this.$realItem.path;
            },
            get config() {
                return this.$realItem.config;
            },
            checkIn($structureItem) {
                return false;
            },
            async getItems(prefix) {
                const realItems = await this.$realItem.getItems(prefix);
                return realItems.map(i => {
                    let linkItem;
                    const draft = Object.assign({ id: i.id }, i.draft);
                    draft.$realItem = i;
                    switch (i.prefix) {
                        case 'C': linkItem = new odaClassLink(draft, this); break;
                        //case 'B': linkItem = new odaBaseLink(draft, this); break;
                        //case 'M': linkItem = new odaModuleLink(draft, this); break;
                        //case 'W': linkItem = new odaWorkplaceLink(draft, this); break;
                        default: return;
                    }
                    return linkItem;
                });
            },
            async delete() {
                if (this.$owner.inherit.isLink) return;

                const links = this.$owner.data['/LINKS/LINK'];
                for (const i in links) {
                    const l = links[i];
                    const link = l['sys:link'] || l['link'];
                    const j = link.lastIndexOf(':');
                    if (link.substr(j - 1) === `${this.prefix}:${this.id}`) {
                        const realItem = await this.findItem(link);
                        if (realItem.path === this.path) {
                            links.splice(i, 1);
                            await this.$owner.save();
                            break;
                        }
                    }
                }
            },
        }) {}
        //#endregion odaStorageLink

        // todo: убрать, когда Аркадий начнёт выдавать ссылки на базы, модули, раб.места
        //#region odaBaseLink
        globalThis.odaBaseLink = class odaBaseLink extends odaBase.ROCKS({
            isLink: true,
            typeLabel: 'base link',
            get $realItem() {
                return this.draft.$realItem;
            },
            get url() {
                return this.$realItem.url;
            },
            get path() {
                return this.$realItem.path;
            },
            get config() {
                return this.$realItem.config;
            },
            checkIn($structureItem) {
                return false;
            },
            async getItems(prefix) {
                const realItems = await this.$realItem.getItems(prefix);
                return realItems.map(i => {
                    let linkItem;
                    const draft = Object.assign({ id: i.id }, i.draft);
                    draft.$realItem = i;
                    switch (i.prefix) {
                        case 'C': linkItem = new odaClassLink(draft, this); break;
                        case 'B': linkItem = new odaBaseLink(draft, this); break;
                        case 'M': linkItem = new odaModuleLink(draft, this); break;
                        case 'W': linkItem = new odaWorkplaceLink(draft, this); break;
                        default: return;
                    }
                    return linkItem;
                });
            },
            async delete() {
                if (this.$owner.inherit.isLink) return;

                const links = this.$owner.data['/LINKS/LINK'];
                for (const i in links) {
                    const l = links[i];
                    const link = l['sys:link'] || l['link'];
                    const j = link.lastIndexOf(':');
                    if (link.substr(j - 1) === `${this.prefix}:${this.id}`) {
                        const realItem = await this.findItem(link);
                        if (realItem.path === this.path) {
                            links.splice(i, 1);
                            await this.$owner.save();
                            break;
                        }
                    }
                }
            },
        }) {}
        //#endregion odaBaseLink

        // todo: убрать, когда Аркадий начнёт выдавать ссылки на базы, модули, раб.места
        //#region odaModuleLink
        globalThis.odaModuleLink = class odaModuleLink extends odaModule.ROCKS({
            typeLabel: 'module link',
            isLink: true,
            get $realItem() {
                return this.draft.$realItem;
            },
            get url() {
                return this.$realItem.url;
            },
            get path() {
                return this.$realItem.path;
            },
            get config() {
                return this.$realItem.config;
            },
            checkIn($structureItem) {
                return false;
            },
            async getItems(prefix) {
                const realItems = await this.$realItem.getItems(prefix);
                return realItems.map(i => {
                    let linkItem;
                    const draft = Object.assign({ id: i.id }, i.draft);
                    draft.$realItem = i;
                    switch (i.prefix) {
                        case 'C': linkItem = new odaClassLink(draft, this); break;
                        //case 'B': linkItem = new odaBaseLink(draft, this); break;
                        case 'M': linkItem = new odaModuleLink(draft, this); break;
                        //case 'W': linkItem = new odaWorkplaceLink(draft, this); break;
                        default: return;
                    }
                    return linkItem;
                });
            },
            import(path) {
                if (this.$realItem.$host.id === this.$host.id)
                    return this.$realItem.import(path);
                return this.$owner.import(path);
            },
            async delete() {
                if (this.$owner.inherit.isLink) return;

                const links = this.$owner.data['/LINKS/LINK'];
                for (const i in links) {
                    const l = links[i];
                    const link = l['sys:link'] || l['link'];
                    const j = link.lastIndexOf(':');
                    if (link.substr(j - 1) === `${this.prefix}:${this.id}`) {
                        const realItem = await this.findItem(link);
                        if (realItem.path === this.path) {
                            links.splice(i, 1);
                            await this.$owner.save();
                            break;
                        }
                    }
                }
            },
        }) {}
        //#endregion odaWorkplaceLink

        // todo: убрать, когда Аркадий начнёт выдавать ссылки на базы, модули, раб.места
        //#region odaWorkplaceLink
        globalThis.odaWorkplaceLink = class odaWorkplaceLink extends odaWorkplace.ROCKS({
            typeLabel: 'work place link',
            isLink: true,
            get $realItem() {
                return this.draft.$realItem;
            },
            get url() {
                return this.$realItem.url;
            },
            get path() {
                return this.$realItem.path;
            },
            get config() {
                return this.$realItem.config;
            },
            checkIn($structureItem) {
                return false;
            },
            async getItems(prefix) {
                const realItems = await this.$realItem.getItems(prefix);
                return realItems.map(i => {
                    let linkItem;
                    const draft = Object.assign({ id: i.id }, i.draft);
                    draft.$realItem = i;
                    switch (i.prefix) {
                        case 'C': linkItem = new odaClassLink(draft, this); break;
                        //case 'B': linkItem = new odaBaseLink(draft, this); break;
                        //case 'M': linkItem = new odaModuleLink(draft, this); break;
                        case 'W': linkItem = new odaWorkplaceLink(draft, this); break;
                        default: return;
                    }
                    return linkItem;
                });
            },
            async delete() {
                if (this.$owner.inherit.isLink) return;

                const links = this.$owner.data['/LINKS/LINK'];
                for (const i in links) {
                    const l = links[i];
                    const link = l['sys:link'] || l['link'];
                    const j = link.lastIndexOf(':');
                    if (link.substr(j - 1) === `${this.prefix}:${this.id}`) {
                        const realItem = await this.findItem(link);
                        if (realItem.path === this.path) {
                            links.splice(i, 1);
                            await this.$owner.save();
                            break;
                        }
                    }
                }
            },
        }) {}
        //#endregion odaWorkplaceLink
    }

    //#region odaFile
    globalThis.odaFile = class odaFile extends odaServerItem.ROCKS({
        type: 'file',
        typeName: 'file',
        prefix: 'F',
        order: 300,
        readOnly: {
            get(){
                return this.$owner.readOnly || this.$sourceItem?.readOnly;
            }
        },
        $$files: [],
        $$folders: [],
        $public: {
            ext: {
                get() {
                    if (this.name.includes('.')) {
                        return (this.name.slice(this.name.lastIndexOf('.') + 1)).toLowerCase();
                    }
                    else {
                        return '';
                    }
                }
            },
            get label() {
                if (this.name.includes('.')) {
                    return (this.name.slice(0, this.name.lastIndexOf('.')));
                }
                else {
                    return this.name;
                }
            },
            get path() {
                return this.$owner.path + '/' + this.id;
            },
        },
        get data() {
            return this.draft;
        },
        get fill() {
            // if (this.inherit && !this.inherit.isSelf && !this.inherit.isOwner && !this.inherit.isLink)
                return this.inherit.color;
            // return `var(--${this.typeName}-color)`;
        },
        get color() {
            return this.fill;
        },
        get icon() {
            switch (this.ext) {
                case 'jpg':
                case 'png':
                    return this.url + '?mode=preview64';
            }
            return 'files:' + this.ext;
        },
        get time() {
            return this.data?.time;
        },
        typeIcon: '',
        get size() {
            return this.data?.size;
        },
        get sizeText() {
            if (this.size && this.size > 0) {
                let v = this.size;
                let pcs;
                if (v < 1000) {
                    pcs = ' b';
                } else if (v < 1000000) {
                    v = Math.round(v / 10) / 100;
                    pcs = ' Kb'
                } else if (v < 1000000000) {
                    v = Math.round(v / 10000) / 100;
                    pcs = ' Mb'
                } else if (v < 1000000000000) {
                    v = Math.round(v / 10000000) / 100;
                    pcs = ' Gb'
                }
                return v.toLocaleString() + pcs;
            } else {
                return '0 b';
            }
        },
        get $sourceItem() {
            let item = this.$owner;
            while (item instanceof odaFile)
                item = item.$owner;
            return item;
        },
        get xml() {
            return this.request('', { method: 'xquery', format: 'xml' }, '*');
        },
        get $storage() {
            return this.$owner.$storage;
        },
        handlersList: {
            $type: Array,
            async get() {
                const structure = await this.$handlerSettingsStorage?._handlerStructure;
                let handlers = [];
                if (!structure) return handlers;

                let proto = this;
                do {
                    let extHandlers = structure[proto.typeName]?.[0]?.[proto.ext];
                    if (extHandlers?.[0]?.H?.length && structure[proto.typeName]?.[0]?.H?.length) {
                        handlers.push(...extHandlers);
                        for (const base of structure[proto.typeName]?.[0]?.H) {
                            if (handlers[0].H.every(h => h.id !== base.id)) {
                                handlers[0].H.push(base);
                            }
                        }
                    }
                    else {
                        handlers.push(...structure[proto.typeName] || []);
                    }
                } while (proto && (proto = proto.__proto__?.__proto__))

                return handlers.flatMap(i => i.H)?.reduce((res, h) => {
                    if (res.every(i => i.name !== h.name)) {
                        res.push(h);
                    }
                    return res;
                }, []);
            }
        },
        hasAttachments: {
            $type: Boolean,
            get() {
                return this.draft.subfile === 'True';
            }
        },
        $attachments: {
            $type: Object,
            async get() {
                if (this.typeName != 'file') return null;
                const cache = this.$owner.itemCache['subfile-folder'] ??= Object.create(null);
                const id = `.${this.id}`;
                const $folder = await (cache[`subfile-${id}`] ??= Promise.resolve(new odaFolder({ id, name: id }, this.$owner)));
                $folder.attachmentsFrom = this;
                return $folder;
            }
        },
        load(params) {
            return this.request('', params).then(res => {
                return this.body = res;
            });
        },
        async save(data, params = {}) {
            if (data || this.body) {
                await this.$owner.saveFile(data || this.body, this.id);
                this.isChanged = false;
            }
        },
        async saveFile(...args) {
            const fileFolder = await this.$attachments;
            this.fire('changed');
            return fileFolder.saveFile(...args);
        },
        async getFile(...args) {
            const fileFolder = await this.$attachments;
            return fileFolder?.getFile(...args);
        },
        async createFile(fileBody, path = '', inherit = true, mime) {
            if (!fileBody) {
                const ext = path.split('/').at(-1).split('.').at(-1);
                if (ext) {
                    const templateUrl = `${this.$sourceItem.url}/~/web/file/extentions/${ext}/template.${ext}`;
                    try {
                        const template = await CORE.request(templateUrl);
                        if (template) fileBody = template;
                    }
                    catch (err) {
                        console.warn(err);
                    }
                }
            }

            const file = await this.saveFile(fileBody, path, inherit, mime);
            if (!file) return;
            file.$owner.reset();
            file.$owner.$storage?.fireServerEvent('files-changed', { operation: 'create', $owner: file.$owner, $F: [file] });
            file.$owner.fire('created', file);
            return file;
        },
        async delete() {
            let result = await this.request('delete');
            result = getBool(result?.result);
            if (result) {
                const info = { path: this.path };
                this.$storage.fireServerEvent('files-changed', { operation: 'delete', $owner: this.$owner, $F: [info] });
                this.fire('delete');
                this.$owner.fire('deleted', this);
                this.$owner.reset?.();
                this.$owner.fire('changed');
            }
            this.reset?.();
            return result;
        },
        reset() {
            this.__customCache__ = {};
            this.itemCache = {};
            this['#$$files'] = undefined;
            this['#$$folders'] = undefined;
            this['#$$handlers'] = undefined;
            this.inherit = undefined;
            this.color = undefined;
            this.fill = undefined;
            this.fire('changed');
        },
        async rename(new_name) {
            // todo: дождаться, когда Аркадий исправит переименование, учитывая наследование (~)
            await this.request('rename_file', { new_name });
        },
        async getNewFileName(name) {
            if (!name) {
                const ext = (await this.$$files)[0]?.ext;
                name = `${this.name}${ext ? `.${ext}` : ''}`;
            }
            const fileFolder = await this.$attachments;
            return fileFolder.getNewFileItemName(name);
        },
        async getNewFolderName(name = 'new folder') {
            const fileFolder = await this.$attachments;
            return fileFolder.getNewFileItemName(name);
        },
        async getNewFileItemName(name) {
            const items = await this.getFilesAndFolders();
            let [n, e] = splitName(name);
            let _name = name;
            let count = n.match(/(?:\((?<count>[0-9]*)\)$)/)?.groups?.count;
            if (count) {
                count = parseInt(count);
                n = n.slice(0, n.indexOf('('));
            }
            else {
                count = 1;
            }
            while (items.some(i => i.name === _name)) {
                count++;
                _name = `${n}(${count})${e ? `.${e}` : ''}`;
            }
            return _name;

            function splitName(name) {
                const idx = name.lastIndexOf('.');
                if (idx > 0) { //иногда единственная точка - первый символ
                    return [name.slice(0, idx), name.slice(idx + 1)];
                }
                return [name, ''];
            }
        },
    }) {
        static defaultPage = 'form';

        static build(draft = {}, owner) { //odaFile
            const type = this.prototype.typeName;
            const cache = owner.itemCache[type] ??= Object.create(null);
            const id = draft.id || draft.name;
            const idx = id.lastIndexOf('.');
            const ext = idx > 0 && type !== 'folder'
                ? ('_' + id.substring(idx + 1))
                : undefined;
            return cache[`${type}-${id}`] ??= owner.getItemCtor(type, ext).then(ctor => {
                return new ctor(draft, owner);
            })
        }
    }
    //#endregion odaFile

    //#region odaHistoryFile
    globalThis.odaHistoryFile = class odaHistoryFile extends odaFile.ROCKS({
        hasChildren: false,
        // get path() {
        //     return `${this.$owner.$owner.path}/.history/${this.$owner.draft.id}/${this.draft.id}`;
        // },
        // get url() {
        //     return `${this.$host.url}${this.path}`;
        // },
        get order() {
            return -this.rawDate;
        },
        get rawDate() {
            const parts = this.name?.split('_');
            // 👀
            // if (parts?.length != 2) {
            //     console.error(`Can't parse file name "${this.name}"`);
            //     return NaN;
            // }

            let dateString = `${parts[0].substr(0, 13)}:${parts[0].substr(14, 2)}:${parts[0].substr(17)}`;
            const date = Date.parse(dateString);
            return date;
        },
        dateEdit: {
            $type: Date,
            get() {
                let date = this.rawDate;
                if (date === NaN) {
                    console.error(`Can't parse date string "${dateString}"`);
                    return;
                }
                date = new Date(date);
                return date;
            }
        },
        userId: {
            $type: String,
            get() {
                const parts = this.name?.split('_');
                if (parts?.length != 2) {
                    console.error(`Can't parse file name "${this.name}"`);
                    return;
                }

                const userId = parts[1]?.split('.')?.[0];
                if (!userId) {
                    console.error(`Can't get user id from file name "${this.name}"`);
                }
                return userId;
            }
        },
        userLabel: {
            $type: String,
            get() {
                this.$host.getUserLabel(this.userId).then((userLabel) => (this.userLabel = userLabel));
                return this.userId;
            }
        },
        label: {
            $type: String,
            get() {
                const dateString = this.dateEdit?.toLocaleString() || '';
                if (this.userLabel instanceof Promise)
                    return `${dateString} - ${this.userId}`;
                return `${dateString} - ${this.userLabel}`;
            }
        }
    }) { }
    //#endregion odaHistoryFile

    const folderTypes = {
        handlers: {
            icon: 'icons:build',
            order: 102
        },
        components: {
            icon: 'icons:apps',
            order: 103
        }
    }

    //#region odaFolder
    globalThis.odaFolder = class odaFolder extends odaFile.ROCKS({
        sizeText: '',
        get icon() {
            return 'odant:folder';
        },
        get subIcon() {
            if(this.isHandler)
                return 'fontawesome:s-gears';
            if(this.isComponent)
                return 'odant:module';

        },
        getOverrides() {
            return this.cache('overrides', async () => {
                if (this.name === '~') return {};
                const dirlist = await this.getDirList({ deep: 3 });
                const web = dirlist.$DIR?.find?.(f => f.name === 'web');
                const overrides = {
                    file: (await this.$owner.getOverrides?.())?.file,
                    folder: (await this.$owner.getOverrides?.())?.folder,
                };
                if (web) {
                    overrides.file = web.$DIR?.find?.(f => f.name === 'file') || overrides.file;
                    overrides.folder = web.$DIR?.find?.(f => f.name === 'folder') || overrides.folder;

                }
                return overrides;
            });
        },
        ext: null,
        get $$history() {
            return [];
        },
        $public: {
            typeName: 'folder',
            get label() {
                if (this.name === '~')
                    return '~FILES~';
                if (this.isCore) {
                    if (this.$sourceItem instanceof odaHost)
                        return 'APP';
                    return 'CORE';
                }
/*                if (!this.isHandler && !this.isComponent/!* && this.role*!/)
                    return this.name.toUpperCase();*/
                return this.name;
            },
            path: {
                $type: String,
                $readOnly: true
            },
        },
        get isHandlerSide() {
            return this.$owner?.isItemFolder && ['server', 'client'].includes(this.id);
        },
        get isComponentLib() {
            return (this.$owner?.isItemFolder && 'lib' === this.id) || (this.$owner?.isComponentLib && !this.isComponent);
        },
        get isFieldsHandlers() {
            return this.$owner?.isItemFolder && 'fields' === this.id;
        },
        get isItemFolder() {
            return this.$owner?.isCore || this.$owner?.isExtentions || this.isExtentions;
        },
        get isCore() {
            return this.$owner?.name === '~' && this.name === 'web'
        },
        get isExtentions() {
            return this.name === 'extentions' && this.$owner?.name === 'file';
        },
        get isComponent() {
            return (this.$owner?.isComponentLib || this.$owner?.isComponent) && this.draft?.$FILE?.some(i => {
                return i.id === this.id + '.js'
            })
        },
        get order() {
            if (this.isCore)
                return -100;
            return this.isItemFolder ? 0 : ((this.isHandler || this.isComponent) ? 200 : 150);
        },
        get count() {
            return this.data?.$FILE?.length;
        },
        get isSystem() {
            return this.id === '~';
        },
        get isHandlersType() {
            return this.$owner?.isHandlerSide || this.$owner?.isFieldsHandlers;
        },
        get isHandler() {
            return (this.$owner?.isHandlersType || this.$owner?.isHandler) && (this.draft?.handler || this.draft?.$FILE?.some(i => {
                return i.id === this.id + '.js'
            }))
        },
        get role() {
            // if (this.isCore)
            //     return 'handlers core';
            // if (this.isItemFolder)
            //     return 'handlers';
            // if (this.isHandlerSide)
            //     return 'handlers side';
            // if (this.isHandlersType)
            //     return 'handlers type';
            // if (this.isHandler)
            //     return 'handler';
            // if (this.isFieldsHandlers)
            //     return 'fields handlers';
            // if (this.isComponentLib)
            //     return 'components'
            // if (this.isComponent)
            //     return 'component'
            return ''
        },
        get typeIcon() {
            if (this.isCore)
                return 'icons:settings';
            return (this.role && (folderTypes[this.role]?.icon) || (this.$owner.typeIcon && this.id));
        },
        get hasChildren() {
            return parseInt(this.data.dirs) || parseInt(this.data.files);
        },
        get $$files() {
            return this.getFiles();
        },
        get $$folders() {
            return this.getFolders();
        },
        attachmentsFrom: null,
        load() {

        },
        reset() {
            this.__customCache__ = {};
            this.itemCache = {};
            this['#$$files'] = undefined;
            this['#$$folders'] = undefined;
            this['#$$handlers'] = undefined;
            this.inherit = undefined;
            this.color = undefined;
            this.fill = undefined;
            this.fire('changed');
            if (this.attachmentsFrom) {
                this.attachmentsFrom.fire('changed');
            }
        },
        async saveFile(fileBody, path = '', inherit = true, mime) {
            let name = path.split('/').pop();
            if (typeof fileBody === 'string') {
                fileBody = new File([fileBody], name, { type: mime || "text/plain" });
            }
            else if (fileBody && typeof fileBody === 'object') {
                if (fileBody instanceof odaFile) {
                    name = fileBody.id;
                    return fileBody.load().then(fileBody => {
                        return this.saveFile(fileBody, path || name);
                    });
                }
                else if (fileBody instanceof File) {
                    if (!path) {
                        path = fileBody.name;
                    }
                }
                else if (Array.isArray(fileBody) || fileBody instanceof Object) {
                    fileBody = new File([JSON.stringify(fileBody)], name, { type: mime || "application/json" });
                }
            }
            else {
                throw new Error('Unknown file type')
            }
            name = name || fileBody.name;
            const formData = new FormData();
            formData.append("file", fileBody, name);
            const url = inherit ? this.url : this.url.replace(/\/~\/?/g, '');
            await CORE.request(`${url}/${path}`, undefined, {}, formData);
            this.reset?.();
            this.fire('changed');
            return this.getFile(path);
        },
        /** @param {File} archive */
        async extractArchive(archive, name = archive.name, params = {}) {
            const formData = new FormData();
            formData.append("file", archive, archive.name);
            return CORE.request(this.url, 'extract', params, formData);
        },
        getDirList(params = {}) {
            return this.request('get_dirlist', params);
        },
        async getFolders(path = '*') {
            if (path.includes('/')) {
                path = path.split('/');
                let step = path.shift();
                let folders = await this.getFolders(step);
                path = path.join('/');
                folders = folders.map(folder => {
                    return folder.getFolders(path);
                })
                folders = await Promise.all(folders);
                return folders.flat();
            }
            let folders = this.itemCache?.folder?.[path];
            if (folders) {
                if (folders?.then) {
                    folders = await folders;
                }
                folders = [folders]
            }
            else {

                folders = await this.getDirList({ mask: path, deep: 3 });
                folders = folders.$DIR;
                folders = folders?.map(draft => {
                    return odaFolder.build(draft, this);
                }) || [];
                folders = await Promise.all(folders);
            }
            return folders;
        },
        getFolder(path) {
            if (!path.includes('/') && path.startsWith('.')) {// костыль
                const cache = this.itemCache['attachments-folder'] ??= Object.create(null);
                return cache[`history-folder-${path}`] ??= Promise.resolve(new odaFolder({ id: path, name: path }, this));
            }
            return this.getFolders(path).then(res => {
                return res[0];
            })
        },
        async getFiles(path = '*') {
            if (path.includes('/')) {
                path = path.split('/');
                let step = path.shift();
                const folders = await this.getFolders(step);
                path = path.join('/')
                let files = folders.map(folder => {
                    return folder.getFiles(path);
                })
                files = await Promise.all(files);
                return files.flat();
            }
            let files = this.itemCache?.folder?.[path];
            if (files) {
                files = [files]
            }
            else {
                files = await this.getDirList({ mask: path, deep: 3 });
                files = files.$FILE?.sort((a, b)=> Date.parse(b.time) - Date.parse(a.time))?.map(async f => {
                    if (this.path.endsWith('/history')) {
                        const cache = this.itemCache['history-file'] ??= Object.create(null);
                        const id = f.id || f.name;
                        return cache[`history-file-${id}`] ??= Promise.resolve(new odaHistoryFile(f, this));
                    }
                    else
                        return odaFile.build(f, this);
                }) || []
                files = await Promise.all(files);
            }
            return files;
        },
        getFile(path) {
            return this.getFiles(path).then(res => {
                return res[0];
            });
        },
        async getFilesAndFolders() {
            return (await Promise.all([this.$$files, this.$$folders])).flat();
        },
        async createFile(fileBody, path = '', inherit = true, mime) {
            if (!fileBody) {
                const ext = path.split('/').at(-1).split('.').at(-1);
                if (ext) {
                    const templateUrl = `${this.$sourceItem.url}/~/web/file/extentions/${ext}/template.${ext}`;
                    try {
                        const template = await CORE.request(templateUrl);
                        if (template) fileBody = template;
                    }
                    catch (err) {
                        console.warn(err);
                    }
                }
            }

            const file = await this.saveFile(fileBody, path, inherit, mime);
            if (!file) return;
            file.$owner.reset();
            file.$owner.$storage?.fireServerEvent('files-changed', { operation: 'create', $owner: file.$owner, $F: [file] });
            file.$owner.fire('created', file);
            return file;
        },
        async createFolder(name) {
            let folder = await this.getFolder(name);
            if (!folder) {
                const file = await this.saveFile('', `${name}/_.dir`);
                // if (!file) return;
                folder = await this.getFolder(name);
            }
            // folder.$owner.reset();
            folder.$owner.$storage.fireServerEvent('files-changed', { operation: 'create', $owner: folder.$owner, $F: [folder] });
            folder.$owner.fire('created', folder);
            return folder;
        },
        async getNewFileName(name) {
            if (!name) {
                const ext = (await this.$$files)[0]?.ext;
                name = `${this.name}${ext ? `.${ext}` : ''}`;
            }
            return this.getNewFileItemName(name);
        },
        async getNewFolderName(name = 'new folder') {
            return this.getNewFileItemName(name);
        },
        checkIn($item) {
            const methods = [];
            if ($item && ([odaFile, File].some(t => $item instanceof t) || $item.isFile || $item.isDirectory) && this.access.allow('A')) {
                methods.add('copy');
                if ($item.access?.allow?.('A')) {
                    methods.add('move');
                }
            }
            return methods;
        }
    }) {
        static defaultPage = 'navigator';

    }
    //#endregion odaFolder

    globalThis.odaHistoryFolder = class odaHistoryFolder extends odaFolder.ROCKS({
        icon: 'icons:history'
    }){}

    //#region odaObject
    globalThis.odaObject = class odaObject extends odaServerItem.ROCKS({extends: [odaIndexOwner, odaFieldOwner],
        prefix: 'O',
        readOnly: {
            $type: Boolean,
            get() {
                return this.access.level < 3 || this.data['sys:read-only'] || this.data.ro;
            },
        },
        $public: {
            get id() {
                return this.data.oid;
            },
            defaultView: {
                $def: null,
                get() {
                    return this.$storage.defaultObjectView;
                },
                set: null
            },
            name: {
                get() {
                    if (this.isStatic) return this.$owner.label;
                    return this.data?.['sys:name'] || this.data?.name || this.data?.['sys:label'] || this.id;
                }
            },
            label: {
                $type: String,
                set(n) {
                    this.data['sys:label'] = n;
                },
                get() {
                    return this.data?.['sys:label'] || this.name;
                }
            },
            icon: {
                get() {
                    let icon = this.draft?.['sys:icon'] || 'odant:object'
                    // if (!icon.includes(':')) icon = `${this.path}/${icon}`
                    return icon
                },
                set(n) {
                    this.data['sys:icon'] = n;
                },
            },
            typeName: 'object',
        },
        get $color() {
            return this.data?.['sys:color'] || '';
        },
        set $color(n) {
            this.data['sys:color'] = n;
        },
        get error() {
            // return this.data; ToDo: надо исправить
        },
        get isDetails() {
            return this.id === this.$base?.id;
        },
        get isStatic() {
            return this.id === this.$storage.id;
        },
        get rootTag() {
            // todo: сервер выдаёт $DETAILS у объекта базы
            return this.isDetails ? '$DETAILS' : this.isStatic ? '$STATIC' : '$OBJECT';
        },
        get typeLabel() {
            return this.isStatic ? 'static' : 'object';
        },
        get $object() {
            return this;
        },
        get $storage() {
            return this.$owner.$storage;
        },
        parentId:{
            $type: String,
            get(){
                return this.data['sys:parent'];
            },
            set(n, old){
                this.data['sys:parent'] = n;
                this.async(async () => {
                    [old, n].forEach(async id => {
                        if (!id) return;
                        const o = await this.$class.$topClass.getObject(id);
                        if (o) {
                            o.$$childrenObjects = undefined;
                            o.fire('changed');
                        }
                    });
                }, 500);
            }
        },
        $parent: {
            $def: null,
            get() {
                return this.parentId ? this.$class.$topClass.getObject(this.parentId) : null;
            },
            set(obj) {
                if (!obj)
                    this.parentId = '';
                else
                    this.parentId = obj.id;
            }
        },
        $$childrenObjects: {
            $type: Array,
            async get(){
                const topClassTableIndex = await this.$class.$topClass.getIndex('table');
                const xq = `
                for $o in x/O[@sys:parent = '${this.id}']
                return element o {
                    attribute path {concat('/B:', $o/@bid, '/C:', $o/@cid, '/O:', $o/@oid)}
                }`;
                const paths = (await topClassTableIndex.XQuery(xq))?.$o || [];
                return Promise.all(paths.map(o => this.$class.$topClass.findItem(o.path)));
            }
        },
        $$connectedObjects: {
            $type: Array,
            async get() {
                // this.listen('changed', () => { this.$$connectedObjects = undefined }, { target: this.$class, once: true });
                const connectionFields = [];
                const $$connectedObjects = [];
                try {
                    for(const f of (await this.$$fields)){
                        if (!f.isTable && !f.type) continue;
                        if(f.type){
                            const $typeClass = await f.$typeClass;
                            if(this.$class.$topClass === $typeClass || this.$class.$topClass.contains($typeClass)){
                                if(f.isTable){
                                    connectionFields.push((await f.$$fields)[0]);
                                }
                                else{
                                    connectionFields.push(f);
                                    continue;
                                }
                            }
                        }
                        if(f.isTable){
                            const $$fields = await f.$$fields;
                            const typedFields = $$fields.filter(f => f.type);
                            if(typedFields.length === 0) continue;
                            for(const tf of typedFields){
                                const $typeClass = await tf.$typeClass;
                                if(this.$class.$topClass === $typeClass || this.$class.$topClass.contains($typeClass)){
                                    connectionFields.push(tf);
                                }
                            }
                        }
                    }
                    if (connectionFields.length > 0) {
                        if (!this.body) {
                            await this.load();
                        }
                        for (const f of connectionFields) {
                            const getLinkedObject = (obj) => this.$class.$topClass.getObject(`/B:${obj.bid}/C:${obj.cid}/O:${obj.oid}`);
                            if(f.$owner.isTable){
                                const path = f.path.slice(0, f.path.lastIndexOf('/')).replace(/\/?METADATA/, '');
                                const subFieldPath = f.path.slice(f.path.lastIndexOf('/')).replace('@', '');
                                const d = this.data[path];
                                for (const row of d) {
                                    const $object = await getLinkedObject(row[subFieldPath][0]);
                                    if($object)
                                        $$connectedObjects.push($object);
                                }
                            }
                            else {
                                const path = f.path.replace(/\/?METADATA/, '').replace('@', '');
                                const obj = this.data[path];
                                const $object = await getLinkedObject(this.data[path][0]);
                                if($object)
                                    $$connectedObjects.push($object);
                            }
                        }
                    }
                }
                catch (err) {
                    console.error('Error on get connectedObjects', err);
                }
                return $$connectedObjects;
            }
        },
        get $$fields() {
            return this.$storage.load().then(() => {
                switch (this.id) {
                    // todo
                    case this.$base?.id || this.$part.id:
                        return this.$storage.$DETAILS.$$fields;
                    case this.$storage.id:
                        return this.$storage.$STATIC.$$fields;
                    default:
                        return Promise.resolve(this.$storage.$FIELDS.$$fields).then(res => res.filter(f => !f.hide));
                }
            });
        },
        hasAttachments: {
            $type: Boolean,
            get() {
                // todo:
                return false;
            }
        },
        $attachments: {
            $type: Array,
            get() {
                return this.$folder;
            }
        },
        load(params = { meta: true, recalc: true }) {
            return this.$super('load', params);
        },
        // todo: пересчёт объекта перед сохранением
        async save(data, params = {}) {
            if (this.readOnly && !params.force) return;
            return new Promise(async (resolve, reject) => {
                const h = async (e) => {
                    if (e.detail?.value?.some?.(i => i.o.includes(this.id))) {
                        this.$storage.unlisten('update', h);
                        if (!this.isChanged) {
                            await this.load();
                            this.isChanged = false;
                        }
                        this.isNew = false;
                        this.fire('after-save', this);
                        resolve(this);
                    }
                }
                this.$storage.listen('update', h);
                try {
                    data = await this.$storage._recalcObject(data || this, true);
                    this.error = data?.$Error;
                    this.body = data?.[this.rootTag][0];

                    if (data || this.body) {
                        this.isChanged = false;
                        await this.request(undefined, params, data || { [this.rootTag]: [this.body] });

                        this.resetHandlers();

                        this.fire('changed');
                    }
                    else
                        throw new Error('no data');
                }
                catch (err) {
                    this.error = err;
                    console.error(err);
                    reject(err);
                }
            });
        },
        async recalc() { // TODO: root => data
            return this.cache('recalc', () => {
                return new Promise((resolve, reject) => {
                    this.debounce('recalc', async () => {
                        if (this.readOnly) return;
                        try {
                            const data = await this.$storage._recalcObject(this, true);
                            this.error = data?.$Error;
                            const root = data?.[this.rootTag][0];
                            const $$fields = await this.$$fields;
                            const merge = async (from, to, path = '') => {
                                const allKeys = Array.from(new Set([...Object.keys(from), ...Object.keys(to)]));
                                const keys = allKeys.filter(k => ['sys:', '$meta:', 'xmlns:'].every(s => !k.startsWith(s)));
                                for (const k of keys) {
                                    const val = from[k];
                                    if (typeof val === 'object') {
                                        await merge(val, to[k], `${path}${isNaN(Number(k)) ? `/${k.replace('$', '')}` : ''}`);
                                    }
                                    else {
                                        const $field = await this.$storage.getField(`${path}/@${k}`);
                                        if($field?.valueExpr && ($field.readOnlyExpr || !to[k])){
                                            to[k] = from[k];
                                        }
                                    }
                                }
                                const sysKeys = allKeys.filter(k => ['sys:', '$meta:', 'xmlns:'].some(s => k.startsWith(s)));
                                for (const k of sysKeys) {
                                    to[k] = from[k];
                                }
                            }
                            await merge(root, this.data);
                            resolve();
                            delete this.__customCache__['recalc'];
                        }
                        catch (e) {
                            this.error = e;
                            console.error(e);
                            reject(e);
                            delete this.__customCache__['recalc'];
                        }
                    }, 400);
                })
            });
        },
        getDataRoot($field, force) {
            const path = ($field.path || $field).replace('[FIELDS]/', '').split('/');
            let root = this.data;
            while (root && path.length > 1) {
                const step = '$' + path.shift();
                let array = root[step];
                if (!array?.length && force) {
                    array = (root[step] = [{}]);
                    root = root[step][0];
                }
                root = array?.[0];
            }
            return root || null;
        },
        getMetadata(field, attr, root, idx) {
            root = root || this.getDataRoot(field);
            field = field.name || field;
            try {
                root = root['$meta:data']?.[0];
                root = root?.['$a:' + field]?.[0];
                root = root?.['$meta:' + attr];
                if (idx !== undefined)
                    root = root?.[idx]?.value;
                return root;
            }
            catch (err) {
                if (idx === undefined)
                    return [];
                return null;
            }
        },
        addRow(field, root) {
            root = root || this.getDataRoot(field);
            let name = '';
            if (field.name) {
                name = field.name;
            }
            else {
                name = field.split('/');
                name = field[field.length - 1];
            }
            root = root['$' + name] = root['$' + name] || [];
            let n = {};
            root.push(n);
            if (field.isTable) {
                let max = root.reduce((res, i) => {
                    if (+i.id > res)
                        res = +i.id;
                    return res;
                }, 0);
                root.filter(i => (!i.id)).map(i => {
                    i.id = ++max;
                });
            }
            this.recalc();
            return n;
        },
        delRow(name, row, root = this.data) {
            if (name && row) {
                let arr = root['$' + name];

                if (!arr || !arr.length) {
                    return;
                }

                let index = row;

                if (typeof row !== 'number') {
                    index = arr.indexOf(row);
                }
                if (index > -1) {
                    let data = arr[index];
                    arr.splice(index, 1);
                    this.recalc();
                    return { index: index, value: data };
                }
            }
        },
        created() {
            if (!SERVER_MODE) {
                const h = async (e) => {
                    if (!this.isChanged && e.detail?.value?.some?.(i => i.o === this.id)) {
                        await this.load();
                        this.fire('changed');
                    }
                }
                this.$storage.listen('update', h);
            }
        },
    }) {}
    //#endregion odaObject

    //#region odaSecurity
    globalThis.odaSecurity = class odaSecurity extends odaItem.ROCKS({ }) {}
    //#endregion odaSecurity

    //#region odaUserObject
    globalThis.odaUserObject = class odaUserObject extends odaSecurity.ROCKS({
        typeName: {
            $public: true,
            $def: 'user-object',
        },
        icon: {
            $type: String,
            get() {
                return (this.online === true) ? 'social:person' : 'social:person-outline';
            }
        },
        online: {
            $type: Boolean,
            get() { return this.data.online; },
        },
        // online: {
        //     $type: Boolean,
        //     get () {
        //         return CORE.request(`${this.$host.url}/U:${this.id}`, 'get_user_is_online')
        //                    .then(res => ((res && ('result' in res)) ? res.result : false));
        //         // CORE.request(`${this.$host.url}/U:${this.id}`, 'get_user_is_online').then(res => {
        //         //     this.online = (res && ('result' in res)) ? res.result : false;
        //         // });
        //     },
        // },
        assigned: {
            $type: Boolean,
            get() { return this.data.assigned; },
        },
        category: {
            $type: String,
            get() { return this.data.category; },
        },
        get $folder() { return this.data.$folder; },
        toJSON(key) {
            return ['id', 'label', 'name', 'icon'].includes(key)
                ? this[key]
                : undefined;
        }
    }) {}
    //#endregion odaUserObject

    //#region odaUser
    globalThis.odaUser = class odaUser extends odaBase.ROCKS({
        prefix: 'O',
        // prefix: 'U',
        get icon() {
            if (this.isGuest) {
                return 'carbon:login';
            }
            else {
                return `@:${this.label.substring(0, 1) || 'G'}`;
            }
        },
        get isNotGuest() {
            return this.name?.toLowerCase() !== 'guest';
        },
        get isGuest() {
            return this.name?.toLowerCase() === 'guest';
        },
        async logout() {
            return this.$host.logout();
        },
        async changePass() {
            //const authHostUrl_res = await fetch(`/api?method=find_account&login=${this.name}`);
            //const authHostUrl_json = await authHostUrl_res.json();
            const authHostUrl_json = await CORE.request(`/api?method=find_account&login=${this.name}`)
            const authHostUrl = authHostUrl_json.result;
            const authHostOrigin = (new URL(authHostUrl)).origin;
            location.assign(`${authHostOrigin}/web/auth/pap/change-pass.html?login=${this.name}&redirect_url=${location}`);
        },
        async open() {
            if (SERVER_MODE) return;
            const origin = (await this.$host.request('find_account', { login: this.name })).result;
            const version = await (async () => {
                try {
                    const pack = await (await fetch(`${origin}/web/package.json`)).json();
                    return parseFloat(pack.version);
                }
                catch (err) {
                    return 1;
                }
            })()

            const hostConfig = await (await fetch(`${origin}/api?config&deep=0`)).json();

            const hostId = hostConfig.$H[0][version >= 3 ? 'id' : 'i'];
            const page = version >= 3
                ? '~/client/pages/navigator/index.html'
                : '~/client/navigator/index.html';
            window.open(`${origin}/api/H:${hostId}/P:USERS/B:${this.id}/${page}`);
        },
        created() {
            if (this.$host?.EventSource) {
                this.$host.EventSource.addUserMessageListener(body => {
                    console.log('User message', body);
                    ODA.push('User message', { body });
                });
            }
            else {
                console.error('EventSource does not exist!');
            }
            this.$host.request('get_user_icon', { id: this.id }).then(res => {
                if(res){
                    this.icon = res;
                }
            });
        }
    }) {}
    //#endregion odaUser

    HANDLER: {
        //#region odaHandler
        globalThis.odaHandler = class odaHandler extends odaServerItem.ROCKS({
            allowUse: true,
            quickTool: false,
            get contextItem() {
                return ((this.$owner instanceof odaHandler) && (this.draft.type !== 'handler')) ? this.$owner.contextItem : this.$owner;
            },
            active: {
                $type: Boolean,
                get() {
                    if (this.draft.source?.endsWith(this.$handlerSettingsStorage.path))
                        return true;
                    const exist = this.data?.id === this.settingsId;
                    if (this.draft.export)
                        return exist && toBool(this.data.active);
                    return !exist || this.data.active?.toLowerCase?.() !== 'false';
                },
                async set(v) {
                    this.data.active = v;
                    this['#isChanged'] = undefined; // ??? сеттер не выполняется когда в кеше есть значение
                }
            },
            $public: {
                id: {
                    $type: String,
                    get() {
                        return this.draft.id
                    }
                },
                name: {
                    $type: String,
                    get() {
                        return this.draft.name
                    }
                },
                category: {
                    $type: String,
                    get() {
                        return this.draft.category
                    }
                },
                get isExported() {
                    return this.draft.export;
                },
                get icon() {
                    return (this.draft?.icon || this.$owner.icon);
                },
                useInModule: false,
                showOnToolBar: false,
                showOnServiceBar: false,
                label: {
                    $type: String,
                    get() {
                        const label = this.name.split('-').join(' ');
                        return label.replace(label[0], label[0].toUpperCase());
                    }
                },
                typeName: 'handler',
            },
            get isChanged() {
                return this.$owner.isChanged;
            },
            set isChanged(v) {
                this.$owner.isChanged = v;
            },
            get formUrl() {
                return this.$owner.formUrl + '#' + this.typeName + '=' + this.name;
            },
            get rootHandler() {
                if (this.$owner instanceof odaHandler && this.contextItem !== this.$owner)
                    return this.$owner.rootHandler;
                return this;
            },
            get rootContext() {
                return this.contextItem instanceof odaHandler
                    ? this.contextItem.rootContext
                    : this.contextItem
            },
            get $folder() {
                return this.rootContext.$handlerSettingsStorage.$folder.then($folder => $folder.getFolder(this.folderPath));
            },
            get $storage() {
                return this.$owner.$storage;
            },
            get folderPath() {
                if (!this.draft.loadUrl) {
                    return this.$owner.folderPath;
                }
                if (this.$owner instanceof odaHandler && this.contextItem !== this.$owner) {
                    return `${this.$owner.folderPath}/${this.draft.name}`;
                }
                return `web/${this.draft.type}/${SIDE}/${this.draft.category}/${this.draft.name}`;
            },
            get filePath() {
                return `${this.$source.path}/~/${this.folderPath}/${this.draft.name}.js`
            },
            get settingsId() {
                return this.$owner instanceof odaHandler && this.contextItem !== this.$owner
                    ? `${this.$owner.settingsId}/${this.id}`
                    : `${this.draft.category}/${this.id}`;
            },
            get settingsPath() {
                return `/HANDLERS[0]/${SIDE.toUpperCase()}[0]/${this.type}[0]/HANDLER[@id='${this.settingsId}']`;
            },
            $source: {
                $type: Object,
            },
            $$customHandlers: [],
            $$childHandlers: {
                $type: Array,
                get() {
                    const customs = this.$$customHandlers || [];
                    return Promise.all(this.draft.H?.map(draft => {
                        return odaHandler.build(draft, this);
                    }) || []).then(async handlers => {
                        handlers = handlers.filter(Boolean);
                        return customs.then?.(custom => {
                            handlers.push(...custom);
                            this.hasChildren = handlers.length > 0;
                            return handlers;
                        }).catch(e => {
                            return handlers;
                        }) || handlers;
                    });
                }
            },
            // get _folderPath() {
            //     if (this.$source instanceof odaFolder) return 'web/folder/' + this.path.split('~/')[2];
            //     if (this.$source instanceof odaFile) return 'web/file/' + this.path.split('~/')[2];
            //     return this.path.split('~/')[1];
            // },
            hasChildren: {
                $type: Boolean,
                get() {
                    return this.$$customHandlers?.length > 0 || this.draft.H?.length > 0;
                }
            },
            get type() {
                if (this.$owner instanceof odaGroup)
                    return this.$owner.$item.typeName.toUpperCase();
                return this.$owner.typeName.toUpperCase();
            },
            get data() {
                if (this.$handlerSettingsStorage instanceof odaHost) return {};
                return this.$handlerSettingsStorage.data[this.settingsPath];
            },
            get root() {
                if (this.$owner instanceof odaObject)
                    return this.$owner.data[this.settingsPath];
                return this.data;
            },
            async execute() {
                throw new Error(`Handler "${this.label}" not implemented!`)
            }
        }) {
            static async build(draft, $owner) { //odaHandler
                //todo: нужен рефакторинг
                const cache = $owner.itemCache[this.prototype.typeName] ??= Object.create(null);
                const id = draft.id;
                return (cache[id] ??= Promise.resolve((async () => {
                    const path = draft.loadUrl?.substring(draft.loadUrl.indexOf('/' + SIDE));
                    let context;
                    if ($owner instanceof odaHandler) {
                        context = draft.type === 'handler'
                            ? context = $owner
                            : context = $owner.contextItem

                        if (!path) {
                            const fn = new Function('prototype', `return class extends this.ROCKS(prototype){}`);
                            const ctor = fn.call(typeMap[$owner.typeName], draft);
                            return new ctor(draft, $owner);
                        }
                    }
                    else {
                        context = $owner
                    }
                    let type = (context instanceof odaGroup) ? context.$item.typeName : context.typeName;
                    let ext = context.ext;
                    let source = $owner;
                    if (draft.export)  //для экспортов
                        source = await source.$base.findItem(draft.source);
                    return source.getHandlerCtor(type, ext, path).then(async ctor => {
                        try {
                            if (ctor.isErrorCtor)
                                throw new Error('Error on get handler ctor: ' + path);
                            const handler = new ctor(draft, $owner);
                            if (!$owner.$handlerSettingsStorage.body) {
                                await $owner.$handlerSettingsStorage.load();
                            }
                            if (draft.source) {
                                handler.$source = await $owner.$handlerSettingsStorage.findItem(draft.source);
                            }
                            else {
                                handler.$source = $owner.$handlerSettingsStorage;
                            }
                            const allowUse = await handler.allowUse;
                            const active = await handler.active;
                            if ($owner.access.allow(handler.allowAccess) && allowUse && active && handler.$source?.disabled !== true)
                                return handler;
                            return null;
                        }
                        catch (e) {
                            console.warn(e);
                            const id = draft.id || draft.i || draft.oid || draft.name || draft.Name;
                            const cache = $owner.itemCache[this.prototype.typeName];
                            if (!cache) return;
                            delete cache[id];

                            const tag = '';
                            if (ODA.wait?.[tag]?.err) {
                                ODA.wait[tag].err(e);
                            } else {
                                ODA.wait[tag] ??= { promise: undefined, reg: undefined, err: undefined };
                                ODA.wait[tag].promise ??= Promise.reject(e);
                            }
                            draft.error = e;
                            const handler = ctor.create(draft, $owner);
                            if (draft.source) {
                                handler.$source = await $owner.$handlerSettingsStorage.findItem(draft.source);
                            }
                            else {
                                handler.$source = $owner.$handlerSettingsStorage;
                            }
                            return handler;
                        }
                    }).catch(async e => {
                        console.error(e);
                        draft.error = e;
                        const handler = new odaHandlerError(draft, $owner);
                        if (draft.source) {
                            handler.$source = await $owner.$handlerSettingsStorage.findItem(draft.source);
                        }
                        else {
                            handler.$source = $owner.$handlerSettingsStorage;
                        }
                        return handler;
                    })

                })()))
            }
        }
        //#endregion odaHandler

        //#region odaHandlerError
        globalThis.odaHandlerError = class odaHandlerError extends odaHandler.ROCKS({
            icon: 'icons:error',
            typeLabel: 'error',
            category: 'error methods',
            get error() {
                return `${this.draft.error}\n\t${this.constructor.error}`;
            },
            execute() {
                throw new Error(this.error);
            }
        }) {
            static isErrorCtor = true;
        }
        //#endregion odaHandlerError

        //#region odaTrigger
        globalThis.odaTrigger = class odaTrigger extends odaHandler.ROCKS({
            typeName: {
                $public: true,
                $def: 'trigger'
            },
        }) {}
        //#endregion odaTrigger

        //#region odaTask
        globalThis.odaTask = class odaTask extends odaHandler.ROCKS({
            $public: {
                schedule: {
                    $type: String,
                    get() {
                        return this.data.schedule
                    },
                    set(v) {
                        this.data.schedule = v
                    }
                },
                typeName: 'task',
            },
            async execute() {
                console.log(`Task "${this.label}" was started.`);
            }
        }) {}
        //#endregion odaTask

        //#region odaServerHandler
        globalThis.odaServerHandler = class odaServerHandler extends odaHandler.ROCKS({
            $public: {
                scheduler: {
                    $type: Object,
                    $editor: '@editors/scheduler'
                }
            },
            scheduleInit() {

            }
        }) {}
        //#endregion odaServerHandler

        //#region odaWidget
        globalThis.odaWidget = class odaWidget extends odaHandler.ROCKS({
            typeName: {
                $public: true,
                $def: 'widget'
            },
            get tagName() {
                if (!this.draft.sourceId)
                    return 'odant-' + this.draft.hierarchy.toLowerCase();
                const matches = this.draft.loadUrl.match(/(?:\/[^\/]*\/(?:(?<type>[^\/]+)\/)(?<side>(?:client)|(?:server))\/(?<category>[^\/]+)\/(?<name>[^\/]+)(?:\/(?:[^\/]+))*\/(?<fileName>(?:[^\/]+)\.js))/)?.groups || {};

                return `odant-${this.draft.hierarchy}-${matches.type}-${this.draft.sourceId}`.toLowerCase();
            },
        }) {}
        //#endregion odaWidget

        //#region odaView
        globalThis.odaView = class odaView extends odaHandler.ROCKS({
            typeName: {
                $public: true,
                $def: 'view'
            },
            hideToolbar: false,
            allowSearch: false,
            get $context() {
                return this.$owner;
            },
            get tagName() {
                if (!this.draft.sourceId)
                    return 'odant-' + this.draft.hierarchy.toLowerCase();
                const matches = this.draft.loadUrl.match(/(?:\/[^\/]*\/(?:(?<type>[^\/]+)\/)(?<side>(?:client)|(?:server))\/(?<category>[^\/]+)\/(?<name>[^\/]+)(?:\/(?:[^\/]+))*\/(?<fileName>(?:[^\/]+)\.js))/)?.groups || {};

                return `odant-${this.draft.hierarchy}-${matches.type}-${this.draft.sourceId}`.toLowerCase();
            },
            get control() {
                // TODO: временное решение, разобраться и исправить причину
                const _ = () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            const cpt = await this.createComponent(this.tagName, { contextItem: this.$context, $handler: this });
                            if (!cpt.constructor.__rocks__) {
                                setTimeout(() => resolve(_()), 100);
                            }
                            else {
                                if (cpt.constructor.__rocks__.descrs.autoSave) {
                                    cpt.constructor.__rocks__.descrs.autoSave.$save = this.allowSave;
                                }
                                resolve(cpt);
                            }
                        }
                        catch (err) {
                            reject(err);
                        }
                    });
                };
                return _();
            },
            execute(float) {
                return this.show(float);
            },
            show(float) {
                if (SERVER_MODE) return;

                let service = ODA.services.getService('form');
                if (
                    service
                    && [odaDomain, odaHost].every(cls => !(service.contextItem instanceof cls))
                    && (
                        [odaObject, odaFile].some(cls => this.contextItem instanceof cls)
                        || (this.contextItem.path === service.contextItem.path)
                        || service.contextItem.contains(this.contextItem)
                    )
                ) {
                    if (typeof service._getAllForms === 'function') {
                        const currentForm = Array.from(service._getAllForms()).find(f => f.contextItem === this.contextItem);
                        if (currentForm) {
                            service = currentForm;
                        }
                    }
                }
                else {
                    service = ODA.services.getService('navigator');
                }

                if (service) {
                    return service._showView(this.contextItem, this.id, float);
                }

                return this.open();
            },
            get openUrl() {
                return this.contextItem.url + '/~/client/pages/form/index.html#view=' + this.id
            },
            open(){
                return window.open( this.openUrl );
            },
            async createComponent(tagName = this.tagName, props = {}) {
                ODANT.tryReg(tagName, this.$owner?.path);
                await ODA.waitReg(tagName)
                const control = await ODANT.createComponent(tagName, props, this.$owner?.path);
                control.contextItem = this.$owner;
                return control;
            }
        }) {}
        //#endregion odaView

        //#region odaPage
        globalThis.odaPage = class odaPage extends odaView.ROCKS({
            // subIcon: 'icons:open-in-new',
            typeName: {
                $public: true,
                $def: 'page'
            },
            get openUrl() {
                return `${this.$source.url}/~/client/pages/${this._path}/index.html`;
            },
            get _path() { //todo надо определиться что такое path для различных типов item
                let path = this.$owner._path;
                if (path)
                    return path + '/' + this.name;
                return this.name;
            },
            execute(parent) {
                return this.open();
            },
            open() {
                if (SERVER_MODE) return;
                return window.open(this.openUrl);
            }
        }) {}
        //#endregion odaPage

        //#region odaMethod
        globalThis.odaMethod = class odaMethod extends odaHandler.ROCKS({
            typeName: {
                $public: true,
                $def: 'method'
            },
            async execute(...args) {
                let buttons = await this.$$childHandlers;
                buttons = await Promise.all(buttons);
                // костыль для диалога
                await Promise.all(buttons.map(b => b.label));
                buttons[0].preFocused = true;
                if (buttons?.length > 1) {
                    try {
                        const result = await this.showConfirm(`Select action\nfor <b>"${this.pathLabel}"</b>`, { buttons, hideOkButton: true })
                        return result?.execute?.(...args);
                    }
                    catch (e) {

                    }
                }
                else if (buttons?.length === 1) {
                    return buttons[0].execute?.(...args);
                }
                else {
                    console.error(`Not defined "execute" function in method "${this.pathLabel}"`, SIDE === 'client' ? this : JSON.stringify(this));
                }
            },
            get pathLabel() {
                return (this.$owner !== this.contextItem) ? (this.$owner.pathLabel + '/' + this.label) : this.label;
            },
            label: {
                $type: String,
                get() {
                    let label = this.$super('label');
                    const h = this.$$childHandlers;
                    if (h.length === 1) {
                        label += ` [${h[0].label}]`;
                        return label;
                    }
                    return label;
                }
            }
        }) {}
        //#endregion odaMethod

        //#region odaFieldControl
        globalThis.odaFieldControl = class odaFieldControl extends odaHandler.ROCKS({
            execute(parent) {}
        }) {}
        //#endregion odaFieldControl
    }

    //#region initialization
    CORE.host = new odaHost({ origin: CORE.URL.origin });
    // CORE.host.$user = new odaUser(new Proxy({}, { has: () => true }), CORE.host);
    CORE.host.config = undefined;
    CORE.host.__customCache__['load-config'] = undefined;
    CORE.host.loadConfig().then(res => {
        CORE.host['#id'] = res.id;
        globalThis.CORE.coreIsReady = true;
        globalThis.dispatchEvent?.(new Event('core-ready'));
    });
    CORE.initUser = function (draft) {
        CORE.host.$user = new odaUser(new Proxy(draft, { has: () => true }), CORE.host);
    };
    //#endregion initialization

    Object.defineProperty(Array.prototype, 'groupBy', {
        enumerable: false, configurable: true, value: function (filter, owner, needGroupAll, needGroupAlone) {
            let groups = this.reduce((res, i) => {
                const key = i[filter] || '';
                let list = res[key];
                if (!list)
                    list = res[key] = [];
                list.push(i);
                return res;
            }, {})
            if ((Object.keys(groups).length < 1) || (needGroupAll && !needGroupAll(groups)))
                return this;
            const items = [];
            for (let i in groups) {
                let group = groups[i];
                if (!i || (needGroupAlone && !needGroupAlone({ name: i, items: group })))
                    items.push(...group);
                else
                    items.push(new odaGroup({ items: group }, owner || group[0].$owner))
            }
            return items;
        }
    });
    if (SERVER_MODE) {
        CORE.__running_handlers ??= new Set();
        CORE.stopServerHandlers ??= function () {
            for (const h of Array.from(CORE.__running_handlers)) {
                try {
                    h.onStopInstance?.();
                }
                catch (err) {
                    console.error(`ERROR on stop instance, in handler "${h.name}", in ${h.$owner.typeName} "${h.$owner.label}"`, err.message);
                }
                finally {
                    CORE.__running_handlers.delete(h);
                }
            }
        }
    }
    CORE.executeHandler = async function (context, method, params, content) {
        const item = await CORE.host.findItem(context);
        if (item) {
            return item.executeHandler(method, params, content);
        }
        throw new Error(`Context "${context}" not found`);
    }
    CORE.registerTask = async function (context, method, { resolveFunc, rejectFunc }, params) {
        const item = await CORE.host.findItem(context);
        if (item) {
            return item.registerTask(method, params, { resolveFunc, rejectFunc });
        }
        rejectFunc(new Error(`Context "${context}" not found`));
    }
    CORE.executeServerHandler = async function (context, method, params, content) {
        const item = await CORE.host.findItem(context);
        if (item) {
            return item.executeServerHandler(method, params, content);
        }
        throw new Error(`Context "${context}" not found`);
    }
    const typeMap = {
        // type names
        'host': odaHost,
        'item': odaItem,
        'class': odaClass,
        'object': odaObject,
        'base': odaBase,
        'part': odaPart,
        'field': odaField,
        'file': odaFile,
        'folder': odaFolder,
        'module': odaModule,
        'workplace': odaWorkplace,
        'domain': odaDomain,
        'index': odaIndex,
        'handler': odaHandler,
        'widget': odaWidget,
        'view': odaView,
        'page': odaPage,
        'trigger': odaTrigger,
        'task': odaTask,
        'method': odaMethod,
        'user-object': odaUserObject,
        // folders
        'widgets': odaWidget,
        'views': odaView,
        'pages': odaPage,
        'triggers': odaTrigger,
        'tasks': odaTask,
        'methods': odaMethod,
    }
}
export default globalThis.CORE;
function genGUID() {
    function _p8(s) {
        var p = (Math.random().toString(16) + "000000000").substr(2, 8);
        return s ? "-" + p.substr(0, 4) + "-" + p.substr(4, 4) : p;
    }
    return _p8() + _p8(true) + _p8(true) + _p8();
}
CORE.genGUID = genGUID;

function renameClass(cls, value) {
    Object.defineProperty(cls, 'name', { value });
    Object.defineProperty(cls.prototype, Symbol.toStringTag, { value });
}

async function getUsers() {
    const allUsers = await this.request('get_users_xml');
    const assignedUsers = await this.request('get_users_xml', { type: 1 });
    let $folder = await this.$host.getFolder(`web/core/user`);
    //let $folder = await this.getFolder(`web/${(this instanceof odaHost) ? 'core/' : ''}user`);
    const result = allUsers?.$USERS?.[0].$U?.map(async (curUser) => {
        let assigned = assignedUsers?.$USERS?.[0].$U?.some((au) => (au.i === curUser.i)) || false;
        const data = {
            id: curUser.i,
            name: curUser.n,
            label: curUser.l || curUser.n,
            email: curUser.e,
            type: curUser.t,
            online: curUser.o === 'T',
            category: curUser.c,
            $folder,
            assigned: assigned
        };
        const userItem = await odaUserObject.build(data, this);
        userItem.assigned = userItem.draft.assigned = data.assigned;
        userItem['#$$handlers'] = undefined;
        Object.keys(userItem.__customCache__)
            .forEach(key => (delete userItem.__customCache__[key]));
        return userItem;
    }) || [];

    // console.log(this, result);
    return Promise.all(result);
}