class WebGpu{
    adapter = null;
    device = null;
    constructor(){
        return new Promise(async resolve=>{
            this.adapter = await navigator.gpu.requestAdapter();
            let maxBufferSize = this.info.maxStorageBufferBindingSize;
            const requiredLimits = {maxStorageBufferBindingSize : maxBufferSize, maxBufferSize};
            this.device = await this.adapter.requestDevice({requiredLimits});
            resolve(this);
        })
    }
    get info(){
        let d_info = Object.getOwnPropertyDescriptors(this.adapter.info.__proto__);
        let info = Object.create(null);
        for(let key in d_info){
            info[key] = this.adapter.info[key];
            if(key === 'memoryHeaps'){
                info[key] = this.adapter.info[key].map(m=>({properties: m.properties, size:m.size}))
            }
        }
        let d_limits = Object.getOwnPropertyDescriptors(this.adapter.limits.__proto__);
        let limits = Object.create(null);
        for(let key in d_limits){
            limits[key] = this.adapter.limits[key];
        }
        return {
            info,
            limits,
        }
    }
}
const webgpu = await (new WebGpu());
export {
    WebGpu,
    webgpu
};
