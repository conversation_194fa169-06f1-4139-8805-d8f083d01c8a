class RocksObject {
  $super(name: string): any;

  async(fn: function, delay?: number);
  throttle(name: string, fn: function, delay?: number);
  debounce(name: string, fn: function, delay?: number);
}

type ItemPrefixes = 'H' | 'P' | 'B' | 'C' | 'W' | 'M' | 'O' | 'U' | 'G' | 'D';
type AccessLevels = 0 | 1 | 2 | 3 | 4 | 5 | 6;
type AccessLetter = 'n' | 'p' | 'r' | 'w' | 'c' | 'd' | 'a';
type AccessLabel = 'admin' | 'delete' | 'create' | 'write' | 'read' | 'preview' | 'none' | 'unknown';
type AccessSign = AccessLevels | AccessLetter | AccessLabel;
type AccessColor = 'var(--access-admin-color)' | 'var(--access-rwcd-color)' | 'var(--access-rwc-color)' | 'var(--access-rw-color)' | 'var(--access-r-color)' | 'var(--access-p-color)';
type ItemTypes = 'class' | 'base' | 'host' | 'domain' | 'module' | 'workplace' | 'part' | 'file' | 'handler' | 'group';

class odaAccesses extends RocksObject {
  constructor(access: AccessSign, owner: odaItem): odaAccesses;
  level: AccessLevels;
  label: AccessLabel;
  color: AccessColors;
  letter: AccessColor;
  isAdmin: boolean;
  allow(access: AccessSign): boolean;
}

class odaItem extends RocksObject {
  constructor(body?: Object, owner?: odaItem): odaItem;
  readonly id: string;
  readonly path: string;
  prefix?: ItemPrefixes;
  name: string;
  icon: string;
  label: string;
  typeName: string;
  typeLabel: string;
  disabled: boolean;

  available: boolean;
  access: odaAccesses;

  $owner: odaItem;

  /** No $class in host */
  $class: odaClass
  /** No $base in host */
  $base: odaBase;
  $host: odaHost;
  $domain: odaDomain;
  $module: odaModule;
  $workplace: odaWorkplace;
  $part: odaPart;

  body: any;

  isNew: boolean;
  isChanged: boolean;
  readOnly: boolean;

  constructor(draft?: Object, owner?: odaItem);

  load(): Promise;
  save(): Promise;

  findItem(path: string, scope = 'context'): Promise<oadItem>;
  getFile(step: string): odaFile;

  resetCache(key?: string): void;

  showMenu(props?:
    {
      title?: string,
      icon?: string,
      contextItem?: odaItem,
      parent?: HTMLElement,
      executeArgs?: any[],
      groups?: string,
      collapseLimit?: number,
      [key: string]: any
    },
    hostProps?: Object
  )

  showInputNameLabelDialog(props: { label?: string, hideName?: boolean, hideLabel?: boolean, namesList: string[] }, dialogProps?: {}, hostProps?: {}): Promise<odaComponent & { name: string, label: string }>
  findStorageItemDialog(props: {
    type: ItemTypes,
    label?: string,
    hideName?: boolean,
    hideLabel?: boolean,
    namesList: string[],
    selectByCheck: boolean,
    findPrefix: ItemPrefixes
  }, dialogProps = {}, hostProps = {}): Promise<odaComponent & { control: odaComponent & { containerHost: Object, selectedRows: Array<{ $item: odaItem }> } }>

  createComponent: ODANT.createComponent;

  static build(draft: {}, $owner?: odaItem): odaItem
}
class odaServerItem extends odaItem {
  defaultView?: string;
}
class odaStructureItem extends odaServerItem {
  draft: { [key: string]: any };
}
class odaStorage extends odaStructureItem {
  $parent?: odaItem;
  getObject(arg: Object | string): Promise<odaObject>;
}
class odaDomain extends odaStorage { }
class odaBase extends odaDomain {
  $owner: odaPart | odaBase;
  inheritDomain(inheritItem: odaDomain, inheritChild = false, silent = false, inheritNoAbstractClassesOnly = false): Promise<*>;
}
class odaModule extends odaDomain {
  $owner: odaPart | odaBase;
}
class odaClass extends odaStorage {
  $parent: odaClass;
  count: number;
  $owner: odaPart | odaBase | odaClass;
  isVirtual: boolean;
}
class odaVirtualItem extends odaItem { }
class odaFile extends odaServerItem {
  $owner: odaFile | odaFolder;
  hasAttachments: boolean;
  $attachments: odaFile[];
}
class odaFolder extends odaFile {
  $owner: odaFolder;
}
class odaHistoryFile extends odaFile { }
class odaObject extends odaServerItem {
  constructor(draft: Object, owner: odaStorage)
}
class odaHandler extends odaServerItem {
  contextItem: odaItem;
  $$childHandlers?: odaHandler[];

  static build(draft: {}, $owner?: odaItem): odaHandler
}
class odaMethod extends odaHandler {
  contextItem: odaItem;
  static build(draft: {}, $owner?: odaItem): odaMethod
}
class odaField extends odaVirtualItem {
  type: string;
  hasFields: boolean;
  isTable: boolean;
  defaultView: string;
  isSystemField: boolean
  hide: boolean;
  virtual: boolean;
  hiddenExpr: string;
  $$fields: Promise<odaField[]>

  createField(body: {}): Promise;
}
class odaExtFields extends odaField { }

class odaGroup extends odaVirtualItem {
  id: string;
  prefix: string;
  label: string;
  icon: string;
  subIcon: string;
  color: string;
  fill: string;
  isRef: boolean;
  hasChildren: boolean;
  typeName: string;
  instructionForAddItem: string;
  count: number;
  order: number;
  path: string;
  $item: odaItem;
  allowInherit: boolean;
  allowMove: boolean;
  isSystem: boolean;
  hide: boolean;

  $$items: odaItem[];
  $$classes: odaClass[];
  $$handlers: Promise<odaHandler[]>;

  delete(): Promise<any>;
  import(...args): Promise<any>;
  getHandlerCtor(type: string, ext: string, path: string): Promise<odaHandler>
}

class odaHandler extends odaVirtualItem { }
class odaView extends odaHandler {
  tagName: string;
  control: Promise<odantComponent>
  $context: odaItem;
  allowButtons: boolean;
  allowSave: boolean;
}

interface Object {
  equal(o1, o2, deep?: boolean): boolean;
}
interface String {
  /**
   * foo-bar => fooBar
   */
  toCamelCase(): string;
}
interface Array<T> {
  last(): T;
  clear(): void;
  remove(...item: T): void;
}

interface ODAServiceManager {
  registerService(name: string, service: any): void;
  getService(name: string): any;
  unregisterService(name: string): void;
}

interface ODA {
  (prototype: object): string;
  cache: {};
  language: string;
  services: ODAServiceManager;
  telemetry: { components: {}, prototypes: {} };
  createComponent(tag: string, props?: {}): odantComponent;
  createElement: ODA.createComponent;
  top: Window & typeof globalThis;
  import: (T) => typeof import(T);
  waitReg: (n: string) => Promise<any>;
  waitDependence: (tag: string) => Promise;
}

interface ODANT extends ODA {
  (prototype: object): Promise<string>;
  origin: string;
  waitIcon: string;
  _fieldViews: {
    'field-input': [],
    'field-ext': [],
    'field-table': [],
    'field-toolbar': [],
  };
  IsReady: boolean;
  deferred: { [key: string]: { promise?: Promise<any>, context?: string, reg?: (context?: string) => Promise } };
  LocalStorage: typeof odaLocalStorage;
  currentUnavailableDialog: odantComponent | null;
  showWelcome: (force?: boolean, contextItem?: odaItem) => Promise<any>;
  showUnavailableDialog: (contextItem: odaItem | string) => Promise<any>;
  show( item: odaServerItem, view?: string, params? = {}, dialog?: boolean)
  tryReg: (name: string, context?: string) => void;
}

class odaLocalStorage extends RocksObject {
  constructor(path: string): odaLocalStorage;
  path: string;
  data: Object;
  getItem(key: string): any;
  getFromItem(key: string, subKey: string): any;
  getByPath(path: string): any;
  setItem(key: string, value: any): void;
  setToItem(key: string, subKey: string, value: any): void;
  setByPath(path: string, value: any): void;
  save(): void;
  version: number;
  clear(): void;
}

interface ComponentPrototype {
  is: string;
  autoSave?: boolean | number | object;
  $system?: { url?: string, dir: string };
  template?: string;
  imports?: string | string[];
  extends?: string | string[];
  $$handlerName?: string;
}

interface odaComponent extends HTMLElement, RocksObject { }
class odaComponent extends HTMLElement {
  $savePath: string;
  $sleep: boolean;
  $keys: { [key: string]: function };

  domHost: odaComponent;

  ready(): void;
  attached(): void;
  detached(): void;

  fire(name: string, details?: { [key: string]: any }): void;
  listen(name: string, handler: Function | keyof this, options?: { target?: EventTarget } & AddEventListenerOptions);
  unlisten(name: string, handler: Function | keyof this, options?: { target?: EventTarget } & AddEventListenerOptions);
  $(selector: string): HTMLElement | null;
  $$(selector: string): [HTMLElement] | [];
  $super(protoTag: string, prop: string, ...args): any;
  $render(): Promise<void>;
  show?: (...args: any[]) => Promise<any>;
}
class odantComponent extends odaComponent {
  contextItem: odaItem;
  isChanged: boolean;
  currentForm?: odantForm;
}

function toBool(val: any, def?: boolean): boolean;
function str2arr(str?: string | string[]): string[];
const CORE_KEY: unique symbol
interface CORE {
  host: odaHost;
  coreIsReady: boolean;
  deferredFields: { [key: string]: Promise[] };
  getContextFromUrl(pathname: string): string
}


type ComponentBuilder = (prototype: ComponentPrototype) => string | Promise<string | void> | void;

namespace globalThis {
  var mainComponentName: string;
  var ODANTUserInfo: Promise<ODANTUserInfoType> | undefined;
  var welcomeWasShown: boolean;
  var wallpaper: any;
  var ODA: ODA;
  var ODANT: ODANT;
  var CORE: CORE;
  var MAIN: ComponentBuilder;
  var VIEW: ComponentBuilder;
  var PAGE: ComponentBuilder;
  var FIELD_INPUT: ComponentBuilder;
  var FIELD_TABLE: ComponentBuilder;
  var FIELD_CONTROL: ComponentBuilder;
  var FIELD_TOOLBAR: ComponentBuilder;
}


type ODANTUserInfoType = {
  login?: string;
  id?: string;
}