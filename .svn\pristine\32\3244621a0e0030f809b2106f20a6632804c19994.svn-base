{"cells": [{"cell_type": "markdown", "metadata": {"id": "V_gWT6I8LSB5"}, "source": ["Code without visible output:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "3Rvp7b6FLSB6"}, "outputs": [], "source": ["a <- 8"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "R-rGEBYDLSB7"}, "outputs": [], "source": ["b <- 4:59"]}, {"cell_type": "markdown", "metadata": {"id": "tIAqBgQ2LSB7"}, "source": ["With visible output:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "RjtlSLLBLSB7", "outputId": "19aa2185-8cdc-470f-e78c-5133972a1de0"}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>12</li><li>13</li><li>14</li><li>15</li><li>16</li><li>17</li><li>18</li><li>19</li><li>20</li><li>21</li><li>22</li><li>23</li><li>24</li><li>25</li><li>26</li><li>27</li><li>28</li><li>29</li><li>30</li><li>31</li><li>32</li><li>33</li><li>34</li><li>35</li><li>36</li><li>37</li><li>38</li><li>39</li><li>40</li><li>41</li><li>42</li><li>43</li><li>44</li><li>45</li><li>46</li><li>47</li><li>48</li><li>49</li><li>50</li><li>51</li><li>52</li><li>53</li><li>54</li><li>55</li><li>56</li><li>57</li><li>58</li><li>59</li><li>60</li><li>61</li><li>62</li><li>63</li><li>64</li><li>65</li><li>66</li><li>67</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 12\n", "\\item 13\n", "\\item 14\n", "\\item 15\n", "\\item 16\n", "\\item 17\n", "\\item 18\n", "\\item 19\n", "\\item 20\n", "\\item 21\n", "\\item 22\n", "\\item 23\n", "\\item 24\n", "\\item 25\n", "\\item 26\n", "\\item 27\n", "\\item 28\n", "\\item 29\n", "\\item 30\n", "\\item 31\n", "\\item 32\n", "\\item 33\n", "\\item 34\n", "\\item 35\n", "\\item 36\n", "\\item 37\n", "\\item 38\n", "\\item 39\n", "\\item 40\n", "\\item 41\n", "\\item 42\n", "\\item 43\n", "\\item 44\n", "\\item 45\n", "\\item 46\n", "\\item 47\n", "\\item 48\n", "\\item 49\n", "\\item 50\n", "\\item 51\n", "\\item 52\n", "\\item 53\n", "\\item 54\n", "\\item 55\n", "\\item 56\n", "\\item 57\n", "\\item 58\n", "\\item 59\n", "\\item 60\n", "\\item 61\n", "\\item 62\n", "\\item 63\n", "\\item 64\n", "\\item 65\n", "\\item 66\n", "\\item 67\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 12\n", "2. 13\n", "3. 14\n", "4. 15\n", "5. 16\n", "6. 17\n", "7. 18\n", "8. 19\n", "9. 20\n", "10. 21\n", "11. 22\n", "12. 23\n", "13. 24\n", "14. 25\n", "15. 26\n", "16. 27\n", "17. 28\n", "18. 29\n", "19. 30\n", "20. 31\n", "21. 32\n", "22. 33\n", "23. 34\n", "24. 35\n", "25. 36\n", "26. 37\n", "27. 38\n", "28. 39\n", "29. 40\n", "30. 41\n", "31. 42\n", "32. 43\n", "33. 44\n", "34. 45\n", "35. 46\n", "36. 47\n", "37. 48\n", "38. 49\n", "39. 50\n", "40. 51\n", "41. 52\n", "42. 53\n", "43. 54\n", "44. 55\n", "45. 56\n", "46. 57\n", "47. 58\n", "48. 59\n", "49. 60\n", "50. 61\n", "51. 62\n", "52. 63\n", "53. 64\n", "54. 65\n", "55. 66\n", "56. 67\n", "\n", "\n"], "text/plain": [" [1] 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36\n", "[26] 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 54 55 56 57 58 59 60 61\n", "[51] 62 63 64 65 66 67"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a + b"]}, {"cell_type": "markdown", "metadata": {"id": "6VN8UxeeLSB8"}, "source": ["Printing is captured and sent to the frontend:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "3o-0jNIrLSB8", "outputId": "85e381be-f440-4839-a7c2-29fa9f354896"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1] \"Hello world! Love, <PERSON> in Jupyter.\"\n"]}], "source": ["print('Hello world! Love, <PERSON> in Jupyter.')"]}, {"cell_type": "markdown", "metadata": {"id": "DinJAwS1LSB8"}, "source": ["So are errors:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tOu7LnUPLSB8"}, "outputs": [], "source": ["f2 <- function() stop('deep error')\n", "throw <- function() f2()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "Jd8zLPhBLSB9", "outputId": "863a372a-f84a-4321-892c-1777efc293f9"}, "outputs": [{"data": {"text/html": ["'this line is run / displayed'"], "text/latex": ["'this line is run / displayed'"], "text/markdown": ["'this line is run / displayed'"], "text/plain": ["[1] \"this line is run / displayed\""]}, "metadata": {}, "output_type": "display_data"}, {"ename": "ERROR", "evalue": "Error in f2(): deep error\n", "output_type": "error", "traceback": ["Error in f2(): deep error\nTraceback:\n", "1. throw()", "2. f2()   # at line 2 of file <text>", "3. stop(\"deep error\")   # at line 1 of file <text>"]}], "source": ["'this line is run / displayed'\n", "throw()\n", "'this line is not run / displayed'"]}, {"cell_type": "markdown", "metadata": {"id": "ilRiqP3ZLSB9"}, "source": ["Plotting works too:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"outputs_hidden": false}, "id": "hpxjb7szLSB9", "outputId": "3eb8d875-7c65-4e75-bc4a-1f15c9511e72"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAIAAAByhViMAAAABmJLR0QA/wD/AP+gvaeTAAAg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\nMkGxAwAAkIn/A++fTTsb1tbqAAAAAElFTkSuQmCC", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["x <- seq(0, 2*pi, length.out=50)\n", "plot(x, sin(x))"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.0.3"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}