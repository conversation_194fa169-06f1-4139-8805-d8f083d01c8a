{"cells": [{"cell_type": "text", "source": ["1234567890"], "metadata": {"id": "text-0"}, "state": "error", "time": ""}, {"cell_type": "code", "source": ["ODA({is: 'oda-demo',\n    template: `\n        <h2>\nКнига первая\nПрибытие марсиан1\nГлава I\nНакануне войны\n        </h2>\n        <div style=\"border: 1px solid red; width: 400px; height: 400px;\"></div>\n        <h4>\nНикто не поверил бы в последние годы девятнадцатого столетия, что за всем происходящим на Земле зорко и внимательно следят существа более развитые, чем человек, хотя такие же смертные, как и он; что в то время, как люди занимались своими делами, их исследовали и изучали, может быть, так же тщательно, как человек в микроскоп изучает эфемерных тварей, кишащих и размножающихся в капле воды\n        </h4>    `,\n    $wake: true\n})\n>'<oda-demo></oda-demo>'"], "metadata": {"id": "13fd51e5b2b", "autoRun": true}}, {"cell_type": "code", "source": ["data = 1234567890\nsetTimeout(() => {\n   fetch('./demo.ipynb').then(res => {\n        res.text().then(txt => {\n            data = txt\n            runNext()\n       })\n   })\n}, 2000)\nbtn = document.createElement('oda-button')\nbtn.label = \"Кнопка\"\n>btn\n>data"], "metadata": {"id": "code-0"}, "time": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-1"}, "time": "", "state": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-2"}, "time": "", "state": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-3"}, "time": "", "state": ""}, {"cell_type": "code", "source": ["for (let i = 0; i < 1000; i++) {\n    let p = await this.progress(i / 1000 * 100)\n    if (!p)\n        break;\n}\n>data"], "metadata": {"id": "85c2210073"}, "time": "", "state": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-31"}, "time": "", "state": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-32"}, "time": "", "state": ""}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-33"}, "time": "", "state": ""}, {"cell_type": "code", "source": "", "metadata": {"id": "d1361bc428"}}, {"cell_type": "code", "source": "", "metadata": {"id": "97b7309255"}}, {"cell_type": "code", "source": "", "metadata": {"id": "b37fe051dc"}}, {"cell_type": "code", "source": "", "metadata": {"id": "329dc63a3f"}}, {"cell_type": "code", "source": "", "metadata": {"id": "136189bfff3"}}, {"cell_type": "code", "source": "", "metadata": {"id": "ca03f2eff"}}, {"cell_type": "code", "source": "", "metadata": {"id": "59194ad543"}}]}