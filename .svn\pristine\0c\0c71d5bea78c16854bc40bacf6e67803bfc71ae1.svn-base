<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WebGPU Matrix Multiplication</title>
</head>
<body>
<script type="module">
    async function initWebGPU() {
        // Проверка поддержки WebGPU
        if (!navigator.gpu) {
            console.error("WebGPU не поддерживается в вашем браузере");
            return;
        }

        // Получаем адаптер и устройство
        const adapter = await navigator.gpu.requestAdapter();
        const device = await adapter.requestDevice();

        // Размер матриц (4x4)
        const matrixSize = 4;
        const bufferSize = matrixSize * matrixSize * 4; // 4 байта на элемент (float32)

        // Создаем матрицы A и B
        const matrixA = new Float32Array([
            1, 2, 3, 4,
            5, 6, 7, 8,
            9, 10, 11, 12,
            13, 14, 15, 16
        ]);

        const matrixB = new Float32Array([
            16, 15, 14, 13,
            12, 11, 10, 9,
            8, 7, 6, 5,
            4, 3, 2, 1
        ]);

        // Создаем буферы на GPU
        const bufferA = device.createBuffer({
            size: bufferSize,
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_DST,
        });

        const bufferB = device.createBuffer({
            size: bufferSize,
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_DST,
        });

        const bufferResult = device.createBuffer({
            size: bufferSize,
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_SRC,
        });

        const readBuffer = device.createBuffer({
            size: bufferSize,
            usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.MAP_READ,
        });

        // Копируем данные на GPU
        device.queue.writeBuffer(bufferA, 0, matrixA);
        device.queue.writeBuffer(bufferB, 0, matrixB);

        // Шейдерный код для умножения матриц
        const shaderCode = `
                @group(0) @binding(0) var<storage, read> a : array<array<f32, ${matrixSize}>, ${matrixSize}>;
                @group(0) @binding(1) var<storage, read> b : array<array<f32, ${matrixSize}>, ${matrixSize}>;
                @group(0) @binding(2) var<storage, read_write> result : array<array<f32, ${matrixSize}>, ${matrixSize}>;

                @compute @workgroup_size(4, 4)
                fn main(@builtin(global_invocation_id) global_id : vec3<u32>) {
                    let row = global_id.y;
                    let col = global_id.x;

                    var sum = 0.0;
                    for (var i = 0u; i < ${matrixSize}u; i = i + 1u) {
                        sum = sum + a[row][i] * b[i][col];
                    }

                    result[row][col] = sum;
                }
            `;

        // Создаем шейдерный модуль
        const shaderModule = device.createShaderModule({
            code: shaderCode,
        });

        // Создаем конвейер вычислений
        const computePipeline = device.createComputePipeline({
            layout: 'auto',
            compute: {
                module: shaderModule,
                entryPoint: 'main',
            },
        });

        // Создаем bind group
        const bindGroup = device.createBindGroup({
            layout: computePipeline.getBindGroupLayout(0),
            entries: [
                {
                    binding: 0,
                    resource: { buffer: bufferA },
                },
                {
                    binding: 1,
                    resource: { buffer: bufferB },
                },
                {
                    binding: 2,
                    resource: { buffer: bufferResult },
                },
            ],
        });

        // Создаем команды и отправляем на выполнение
        const commandEncoder = device.createCommandEncoder();
        const passEncoder = commandEncoder.beginComputePass();
        passEncoder.setPipeline(computePipeline);
        passEncoder.setBindGroup(0, bindGroup);
        passEncoder.dispatchWorkgroups(1); // 4x4 threads (по одному на элемент матрицы)
        passEncoder.end();

        // Копируем результат в буфер для чтения
        commandEncoder.copyBufferToBuffer(
            bufferResult,
            0,
            readBuffer,
            0,
            bufferSize
        );

        // Отправляем команды
        device.queue.submit([commandEncoder.finish()]);

        // Читаем результат
        await readBuffer.mapAsync(GPUMapMode.READ);
        const result = new Float32Array(readBuffer.getMappedRange().slice(0));
        readBuffer.unmap();

        console.log("Matrix A:", matrixA);
        console.log("Matrix B:", matrixB);
        console.log("Result:", result);

        // Форматируем вывод для лучшей читаемости
        function formatMatrix(arr, size) {
            let str = '';
            for (let i = 0; i < size; i++) {
                str += arr.slice(i * size, (i + 1) * size).join(', ') + '\n';
            }
            return str;
        }

        console.log("Matrix A:\n" + formatMatrix(matrixA, matrixSize));
        console.log("Matrix B:\n" + formatMatrix(matrixB, matrixSize));
        console.log("Result:\n" + formatMatrix(result, matrixSize));
    }

    initWebGPU();
</script>
</body>
</html>