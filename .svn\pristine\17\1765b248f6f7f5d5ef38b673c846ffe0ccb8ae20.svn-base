{"cells": [{"cell_type": "text", "source": ["1234567890"], "metadata": {"id": "text-0"}, "state": "error"}, {"cell_type": "code", "source": ["data = 1234567890\nsetTimeout(() => {\n   data = '0987654321'\n   runNext()\n}, 1000)\n>data"], "metadata": {"id": "code-0"}}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-1"}}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-2"}}, {"cell_type": "code", "source": [">data"], "metadata": {"id": "code-3"}}]}