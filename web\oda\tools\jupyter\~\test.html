<meta charset="UTF-8">
<title>Title</title>
<script type="module">
    import '../../oda.js'
    const text1 = '  a = 100;' +
        'console.log(a)'
    const text2 = 'b = 200 * a'
    const fn1 = new Function('', `with (this) {${text1}}`)
    const fn2 = new Function('', `with (this) {${text2}}`)
    const result1 = fn1.call(window)
    const result2 = fn2.call(window)

    console.log(a, b)
    ODA({ is: 'oda-component2', template:`dfddd`
    })

</script>
<oda-component2></oda-component2>