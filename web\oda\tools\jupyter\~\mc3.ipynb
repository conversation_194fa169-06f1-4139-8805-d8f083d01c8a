{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Probabilistic Programming\n", "=====\n", "and Bayesian Methods for Hackers \n", "========\n", "\n", "##### Version 0.1\n", "\n", "`Original content created by <PERSON>`\n", "\n", "`Ported to Python 3 and PyMC3 by <PERSON> (@clean_utensils) and <PERSON> (@twiecki) at Quantopian (@quantopian)`\n", "___\n", "\n", "\n", "Welcome to *Bayesian Methods for Hackers*. The full Github repository is available at [github/Probabilistic-Programming-and-Bayesian-Methods-for-Hackers](https://github.com/CamDavidsonPilon/Probabilistic-Programming-and-Bayesian-Methods-for-Hackers). The other chapters can be found on the project's [homepage](https://camdavidsonpilon.github.io/Probabilistic-Programming-and-Bayesian-Methods-for-Hackers/). We hope you enjoy the book, and we encourage any contributions!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Chapter 1\n", "======\n", "***"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Philosophy of Bayesian Inference\n", "------\n", "  \n", "> You are a skilled programmer, but bugs still slip into your code. After a particularly difficult implementation of an algorithm, you decide to test your code on a trivial example. It passes. You test the code on a harder problem. It passes once again. And it passes the next, *even more difficult*, test too! You are starting to believe that there may be no bugs in this code...\n", "\n", "If you think this way, then congratulations, you already are thinking Bayesian! Bayesian inference is simply updating your beliefs after considering new evidence. A Bayesian can rarely be certain about a result, but he or she can be very confident. Just like in the example above, we can never be 100% sure that our code is bug-free unless we test it on every possible problem; something rarely possible in practice. Instead, we can test it on a large number of problems, and if it succeeds we can feel more *confident* about our code, but still not certain.  Bayesian inference works identically: we update our beliefs about an outcome; rarely can we be absolutely sure unless we rule out all other alternatives. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### The Bayesian state of mind\n", "\n", "\n", "Bayesian inference differs from more traditional statistical inference by preserving *uncertainty*. At first, this sounds like a bad statistical technique. Isn't statistics all about deriving *certainty* from randomness? To reconcile this, we need to start thinking like Bayesians. \n", "\n", "The Bayesian world-view interprets probability as measure of *believability in an event*, that is, how confident we are in an event occurring. In fact, we will see in a moment that this is the natural interpretation of probability. \n", "\n", "For this to be clearer, we consider an alternative interpretation of probability: *Frequentist*, known as the more *classical* version of statistics, assume that probability is the long-run frequency of events (hence the bestowed title). For example, the *probability of plane accidents* under a frequentist philosophy is interpreted as the *long-term frequency of plane accidents*. This makes logical sense for many probabilities of events, but becomes more difficult to understand when events have no long-term frequency of occurrences. Consider: we often assign probabilities to outcomes of presidential elections, but the election itself only happens once! Frequentists get around this by invoking alternative realities and saying across all these realities, the frequency of occurrences defines the probability. \n", "\n", "Bayesians, on the other hand, have a more intuitive approach. Bayesians interpret a probability as measure of *belief*, or confidence, of an event occurring. Simply, a probability is a summary of an opinion. An individual who assigns a belief of 0 to an event has no confidence that the event will occur; conversely, assigning a belief of 1 implies that the individual is absolutely certain of an event occurring. Beliefs between 0 and 1 allow for weightings of other outcomes. This definition agrees with the probability of a plane accident example, for having observed the frequency of plane accidents, an individual's belief should be equal to that frequency, excluding any outside information. Similarly, under this definition of probability being equal to beliefs, it is meaningful to speak about probabilities (beliefs) of presidential election outcomes: how confident are you candidate *A* will win?\n", "\n", "Notice in the paragraph above, I assigned the belief (probability) measure to an *individual*, not to Nature. This is very interesting, as this definition leaves room for conflicting beliefs between individuals. Again, this is appropriate for what naturally occurs: different individuals have different beliefs of events occurring, because they possess different *information* about the world. The existence of different beliefs does not imply that anyone is wrong. Consider the following examples demonstrating the relationship between individual beliefs and probabilities:\n", "\n", "- I flip a coin, and we both guess the result. We would both agree, assuming the coin is fair, that the probability of Heads is 1/2. Assume, then, that I peek at the coin. Now I know for certain what the result is: I assign probability 1.0 to either <PERSON> or <PERSON>ls (whichever it is). Now what is *your* belief that the coin is Heads? My knowledge of the outcome has not changed the coin's results. Thus we assign different probabilities to the result. \n", "\n", "-  Your code either has a bug in it or not, but we do not know for certain which is true, though we have a belief about the presence or absence of a bug.  \n", "\n", "-  A medical patient is exhibiting symptoms $x$, $y$ and $z$. There are a number of diseases that could be causing all of them, but only a single disease is present. A doctor has beliefs about which disease, but a second doctor may have slightly different beliefs. \n", "\n", "\n", "This philosophy of treating beliefs as probability is natural to humans. We employ it constantly as we interact with the world and only see partial truths, but gather evidence to form beliefs. Alternatively, you have to be *trained* to think like a frequentist. \n", "\n", "To align ourselves with traditional probability notation, we denote our belief about event $A$ as $P(A)$. We call this quantity the *prior probability*.\n", "\n", "<PERSON>, a great economist and thinker, said \"When the facts change, I change my mind. What do you do, sir?\" This quote reflects the way a Bayesian updates his or her beliefs after seeing evidence. Even &mdash; especially &mdash; if the evidence is counter to what was initially believed, the evidence cannot be ignored. We denote our updated belief as $P(A |X )$, interpreted as the probability of $A$ given the evidence $X$. We call the updated belief the *posterior probability* so as to contrast it with the prior probability. For example, consider the posterior probabilities (read: posterior beliefs) of the above examples, after observing some evidence $X$:\n", "\n", "1\\. $P(A): \\;\\;$ the coin has a 50 percent chance of being Heads. $P(A | X):\\;\\;$ You look at the coin, observe a Heads has landed, denote this information $X$, and trivially assign probability 1.0 to <PERSON> and 0.0 to <PERSON><PERSON>.\n", "\n", "2\\.   $P(A): \\;\\;$  This big, complex code likely has a bug in it. $P(A | X): \\;\\;$ The code passed all $X$ tests; there still might be a bug, but its presence is less likely now.\n", "\n", "3\\.  $P(A):\\;\\;$ The patient could have any number of diseases. $P(A | X):\\;\\;$ Performing a blood test generated evidence $X$, ruling out some of the possible diseases from consideration.\n", "\n", "\n", "It's clear that in each example we did not completely discard the prior belief after seeing new evidence $X$, but we *re-weighted the prior* to incorporate the new evidence (i.e. we put more weight, or confidence, on some beliefs versus others). \n", "\n", "By introducing prior uncertainty about events, we are already admitting that any guess we make is potentially very wrong. After observing data, evidence, or other information, we update our beliefs, and our guess becomes *less wrong*. This is the alternative side of the prediction coin, where typically we try to be *more right*. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Bayesian Inference in Practice\n", "\n", " If frequentist and Bayesian inference were programming functions, with inputs being statistical problems, then the two would be different in what they return to the user. The frequentist inference function would return a number, representing an estimate (typically a summary statistic like the sample average etc.), whereas the Bayesian function would return *probabilities*.\n", "\n", "For example, in our debugging problem above, calling the frequentist function with the argument \"My code passed all $X$ tests; is my code bug-free?\" would return a *YES*. On the other hand, asking our Bayesian function \"Often my code has bugs. My code passed all $X$ tests; is my code bug-free?\" would return something very different: probabilities of *YES* and *NO*. The function might return:\n", "\n", "\n", ">    *YES*, with probability 0.8; *NO*, with probability 0.2\n", "\n", "\n", "\n", "This is very different from the answer the frequentist function returned. Notice that the Bayesian function accepted an additional argument:  *\"Often my code has bugs\"*. This parameter is the *prior*. By including the prior parameter, we are telling the Bayesian function to include our belief about the situation. Technically this parameter in the Bayesian function is optional, but we will see excluding it has its own consequences. \n", "\n", "\n", "#### Incorporating evidence\n", "\n", "As we acquire more and more instances of evidence, our prior belief is *washed out* by the new evidence. This is to be expected. For example, if your prior belief is something ridiculous, like \"I expect the sun to explode today\", and each day you are proved wrong, you would hope that any inference would correct you, or at least align your beliefs better. Bayesian inference will correct this belief.\n", "\n", "\n", "Denote $N$ as the number of instances of evidence we possess. As we gather an *infinite* amount of evidence, say as $N \\rightarrow \\infty$, our Bayesian results (often) align with frequentist results. Hence for large $N$, statistical inference is more or less objective. On the other hand, for small $N$, inference is much more *unstable*: frequentist estimates have more variance and larger confidence intervals. This is where Bayesian analysis excels. By introducing a prior, and returning probabilities (instead of a scalar estimate), we *preserve the uncertainty* that reflects the instability of statistical inference of a small $N$ dataset. \n", "\n", "One may think that for large $N$, one can be indifferent between the two techniques since they offer similar inference, and might lean towards the computationally-simpler, frequentist methods. An individual in this position should consider the following quote by <PERSON> (2005)[1], before making such a decision:\n", "\n", "> Sample sizes are never large. If $N$ is too small to get a sufficiently-precise estimate, you need to get more data (or make more assumptions). But once $N$ is \"large enough,\" you can start subdividing the data to learn more (for example, in a public opinion poll, once you have a good estimate for the entire country, you can estimate among men and women, northerners and southerners, different age groups, etc.). $N$ is never enough because if it were \"enough\" you'd already be on to the next problem for which you need more data.\n", "\n", "### Are frequentist methods incorrect then? \n", "\n", "**No.**\n", "\n", "Frequentist methods are still useful or state-of-the-art in many areas. Tools such as least squares linear regression, LASSO regression, and expectation-maximization algorithms are all powerful and fast. Bayesian methods complement these techniques by solving problems that these approaches cannot, or by illuminating the underlying system with more flexible modeling.\n", "\n", "\n", "#### A note on *Big Data*\n", "Paradoxically, big data's predictive analytic problems are actually solved by relatively simple algorithms [2][4]. Thus we can argue that big data's prediction difficulty does not lie in the algorithm used, but instead on the computational difficulties of storage and execution on big data. (One should also consider <PERSON><PERSON><PERSON>'s quote from above and ask \"Do I really have big data?\")\n", "\n", "The much more difficult analytic problems involve *medium data* and, especially troublesome, *really small data*. Using a similar argument as  <PERSON><PERSON><PERSON>'s above, if big data problems are *big enough* to be readily solved, then we should be more interested in the *not-quite-big enough* datasets. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Our Bayesian framework\n", "\n", "We are interested in beliefs, which can be interpreted as probabilities by thinking Bayesian. We have a *prior* belief in event $A$, beliefs formed by previous information, e.g., our prior belief about bugs being in our code before performing tests.\n", "\n", "Secondly, we observe our evidence. To continue our buggy-code example: if our code passes $X$ tests, we want to update our belief to incorporate this. We call this new belief the *posterior* probability. Updating our belief is done via the following equation, known as <PERSON><PERSON>' Theorem, after its discoverer <PERSON>:\n", "\n", "\\begin{align}\n", " P( A | X ) = & \\frac{ P(X | A) P(A) } {P(X) } \\\\\\\\[5pt]\n", "& \\propto P(X | A) P(A)\\;\\; (\\propto \\text{is proportional to })\n", "\\end{align}\n", "\n", "The above formula is not unique to Bayesian inference: it is a mathematical fact with uses outside Bayesian inference. Bayesian inference merely uses it to connect prior probabilities $P(A)$ with an updated posterior probabilities $P(A | X )$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example: Mandatory coin-flip example\n", "\n", "Every statistics text must contain a coin-flipping example, I'll use it here to get it out of the way. Suppose, naively, that you are unsure about the probability of heads in a coin flip (spoiler alert: it's 50%). You believe there is some true underlying ratio, call it $p$, but have no prior opinion on what $p$ might be. \n", "\n", "We begin to flip a coin, and record the observations: either $H$ or $T$. This is our observed data. An interesting question to ask is how our inference changes as we observe more and more data? More specifically, what do our posterior probabilities look like when we have little data, versus when we have lots of data. \n", "\n", "Below we plot a sequence of updating posterior probabilities as we observe increasing amounts of data (coin flips)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxAAAAKbCAYAAABl+WxSAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nOhAiIiIiIhKaOhAiIiIiIhLa/wNELXdUsJ1f3wAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53fc284550>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\"\"\"\n", "The book uses a custom matplotlibrc file, which provides the unique styles for\n", "matplotlib plots. If executing this book, and you wish to use the book's\n", "styling, provided are two options:\n", "    1. Overwrite your own matplotlibrc file with the rc-file provided in the\n", "       book's styles/ dir. See http://matplotlib.org/users/customizing.html\n", "    2. Also in the styles is  bmh_matplotlibrc.json file. This can be used to\n", "       update the styles in only this notebook. Try running the following code:\n", "\n", "        import json\n", "        s = json.load(open(\"../styles/bmh_matplotlibrc.json\"))\n", "        matplotlib.rcParams.update(s)\n", "\n", "\"\"\"\n", "\n", "# The code below can be passed over, as it is currently not important, plus it\n", "# uses advanced topics we have not covered yet. LOOK AT PICTURE, MICHAEL!\n", "%matplotlib inline\n", "from IPython.core.pylabtools import figsize\n", "import numpy as np\n", "from matplotlib import pyplot as plt\n", "figsize(11, 9)\n", "\n", "import scipy.stats as stats\n", "\n", "dist = stats.beta\n", "n_trials = [0, 1, 2, 3, 4, 5, 8, 15, 50, 500]\n", "data = stats.bernoulli.rvs(0.5, size=n_trials[-1])\n", "x = np.linspace(0, 1, 100)\n", "\n", "# For the already prepared, I'm using Binomial's conj. prior.\n", "for k, N in enumerate(n_trials):\n", "    sx = plt.subplot(len(n_trials)//2, 2, k+1)\n", "    plt.xlabel(\"$p$, probability of heads\") \\\n", "        if k in [0, len(n_trials)-1] else None\n", "    plt.setp(sx.get_yticklabels(), visible=False)\n", "    heads = data[:N].sum()\n", "    y = dist.pdf(x, 1 + heads, 1 + N - heads)\n", "    plt.plot(x, y, label=\"observe %d tosses,\\n %d heads\" % (N, heads))\n", "    plt.fill_between(x, 0, y, color=\"#348ABD\", alpha=0.4)\n", "    plt.vlines(0.5, 0, 4, color=\"k\", linestyles=\"--\", lw=1)\n", "\n", "    leg = plt.legend()\n", "    leg.get_frame().set_alpha(0.4)\n", "    plt.autoscale(tight=True)\n", "\n", "\n", "plt.suptitle(\"Bayesian updating of posterior probabilities\",\n", "             y=1.02,\n", "             fontsize=14)\n", "\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The posterior probabilities are represented by the curves, and our uncertainty is proportional to the width of the curve. As the plot above shows, as we start to observe data our posterior probabilities start to shift and move around. Eventually, as we observe more and more data (coin-flips), our probabilities will tighten closer and closer around the true value of $p=0.5$ (marked by a dashed line). \n", "\n", "Notice that the plots are not always *peaked* at 0.5. There is no reason it should be: recall we assumed we did not have a prior opinion of what $p$ is. In fact, if we observe quite extreme data, say 8 flips and only 1 observed heads, our distribution would look very biased *away* from lumping around 0.5 (with no prior opinion, how confident would you feel betting on a fair coin after observing 8 tails and 1 head?). As more data accumulates, we would see more and more probability being assigned at $p=0.5$, though never all of it.\n", "\n", "The next example is a simple demonstration of the mathematics of Bayesian inference. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example: Bug, or just sweet, unintended feature?\n", "\n", "\n", "Let $A$ denote the event that our code has **no bugs** in it. Let $X$ denote the event that the code passes all debugging tests. For now, we will leave the prior probability of no bugs as a variable, i.e. $P(A) = p$. \n", "\n", "We are interested in $P(A|X)$, i.e. the probability of no bugs, given our debugging tests $X$. To use the formula above, we need to compute some quantities.\n", "\n", "What is $P(X | A)$, i.e., the probability that the code passes $X$ tests *given* there are no bugs? Well, it is equal to 1, for a code with no bugs will pass all tests. \n", "\n", "$P(X)$ is a little bit trickier: The event $X$ can be divided into two possibilities, event $X$ occurring even though our code *indeed has* bugs (denoted $\\sim A\\;$, spoken *not $A$*), or event $X$ without bugs ($A$). $P(X)$ can be represented as:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\\begin{align}\n", "P(X ) & = P(X \\text{ and } A) + P(X \\text{ and } \\sim A) \\\\\\\\[5pt]\n", " & = P(X|A)P(A) + P(X | \\sim A)P(\\sim A)\\\\\\\\[5pt]\n", "& = P(X|A)p + P(X | \\sim A)(1-p)\n", "\\end{align}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We have already computed $P(X|A)$ above. On the other hand, $P(X | \\sim A)$ is subjective: our code can pass tests but still have a bug in it, though the probability there is a bug present is reduced. Note this is dependent on the number of tests performed, the degree of complication in the tests, etc. Let's be conservative and assign $P(X|\\sim A) = 0.5$. Then\n", "\n", "\\begin{align}\n", "P(A | X) & = \\frac{1\\cdot p}{ 1\\cdot p +0.5 (1-p) } \\\\\\\\\n", "& = \\frac{ 2 p}{1+p}\n", "\\end{align}\n", "This is the posterior probability. What does it look like as a function of our prior, $p \\in [0,1]$? "]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAvoAAAEiCAYAAACImnYKAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nzSwH+KC7f3W0ggtDPfoiIjJYR08fr9UGhf0rR5rZe7KdM/******************************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\nLIf+DyJv+65PsOkIAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53fc28cfd0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 4)\n", "p = np.linspace(0, 1, 50)\n", "plt.plot(p, 2*p/(1+p), color=\"#348ABD\", lw=3)\n", "#plt.fill_between(p, 2*p/(1+p), alpha=.5, facecolor=[\"#A60628\"])\n", "plt.scatter(0.2, 2*(0.2)/1.2, s=140, c=\"#348ABD\")\n", "plt.xlim(0, 1)\n", "plt.ylim(0, 1)\n", "plt.xlabel(\"Prior, $P(A) = p$\")\n", "plt.ylabel(\"Posterior, $P(A|X)$, with $P(A) = p$\")\n", "plt.title(\"Are there bugs in my code?\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the biggest gains if we observe the $X$ tests passed when the prior probability, $p$, is low. Let's settle on a specific value for the prior. I'm a strong programmer (I think), so I'm going to give myself a realistic prior of 0.20, that is, there is a 20% chance that I write code bug-free. To be more realistic, this prior should be a function of how complicated and large the code is, but let's pin it at 0.20. Then my updated belief that my code is bug-free is 0.33. \n", "\n", "Recall that the prior is a probability: $p$ is the prior probability that there *are no bugs*, so $1-p$ is the prior probability that there *are bugs*.\n", "\n", "Similarly, our posterior is also a probability, with $P(A | X)$ the probability there is no bug *given we saw all tests pass*, hence $1-P(A|X)$ is the probability there is a bug *given all tests passed*. What does our posterior probability look like? Below is a chart of both the prior and the posterior probabilities. \n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAu0AAAELCAYAAABtS7hlAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\npF1EREREJM39f/YawOkdCGYlAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53c6a13390>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 4)\n", "colours = [\"#348ABD\", \"#A60628\"]\n", "\n", "prior = [0.20, 0.80]\n", "posterior = [1./3, 2./3]\n", "plt.bar([0, .7], prior, alpha=0.70, width=0.25,\n", "        color=colours[0], label=\"prior distribution\",\n", "        lw=\"3\", edgecolor=colours[0])\n", "\n", "plt.bar([0+0.25, .7+0.25], posterior, alpha=0.7,\n", "        width=0.25, color=colours[1],\n", "        label=\"posterior distribution\",\n", "        lw=\"3\", edgecolor=colours[1])\n", "\n", "plt.xticks([0.20, .95], [\"<PERSON> Absent\", \"Bugs Present\"])\n", "plt.title(\"Prior and Posterior probability of bugs present\")\n", "plt.ylabel(\"Probability\")\n", "plt.legend(loc=\"upper left\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Notice that after we observed $X$ occur, the probability of bugs being absent increased. By increasing the number of tests, we can approach confidence (probability 1) that there are no bugs present.\n", "\n", "This was a very simple example of Bayesian inference and <PERSON><PERSON> rule. Unfortunately, the mathematics necessary to perform more complicated Bayesian inference only becomes more difficult, except for artificially constructed cases. We will later see that this type of mathematical analysis is actually unnecessary. First we must broaden our modeling tools. The next section deals with *probability distributions*. If you are already familiar, feel free to skip (or at least skim), but for the less familiar the next section is essential."]}, {"cell_type": "markdown", "metadata": {}, "source": ["_______\n", "\n", "## Probability Distributions\n", "\n", "\n", "**Let's quickly recall what a probability distribution is:** Let $Z$ be some random variable. Then associated with $Z$ is a *probability distribution function* that assigns probabilities to the different outcomes $Z$ can take. Graphically, a probability distribution is a curve where the probability of an outcome is proportional to the height of the curve. You can see examples in the first figure of this chapter. \n", "\n", "We can divide random variables into three classifications:\n", "\n", "-   **$Z$ is discrete**: Discrete random variables may only assume values on a specified list. Things like populations, movie ratings, and number of votes are all discrete random variables. Discrete random variables become more clear when we contrast them with...\n", "\n", "-   **$Z$ is continuous**: Continuous random variable can take on arbitrarily exact values. For example, temperature, speed, time, color are all modeled as continuous variables because you can progressively make the values more and more precise.\n", "\n", "- **$Z$ is mixed**: Mixed random variables assign probabilities to both discrete and continuous random variables, i.e. it is a combination of the above two categories. \n", "\n", "### Discrete Case\n", "If $Z$ is discrete, then its distribution is called a *probability mass function*, which measures the probability $Z$ takes on the value $k$, denoted $P(Z=k)$. Note that the probability mass function completely describes the random variable $Z$, that is, if we know the mass function, we know how $Z$ should behave. There are popular probability mass functions that consistently appear: we will introduce them as needed, but let's introduce the first very useful probability mass function. We say $Z$ is *Poisson*-distributed if:\n", "\n", "$$P(Z = k) =\\frac{ \\lambda^k e^{-\\lambda} }{k!}, \\; \\; k=0,1,2, \\dots $$\n", "\n", "$\\lambda$ is called a parameter of the distribution, and it controls the distribution's shape. For the Poisson distribution, $\\lambda$ can be any positive number. By increasing $\\lambda$, we add more probability to larger values, and conversely by decreasing $\\lambda$ we add more probability to smaller values. One can describe $\\lambda$ as the *intensity* of the Poisson distribution. \n", "\n", "Unlike $\\lambda$, which can be any positive number, the value $k$ in the above formula must be a non-negative integer, i.e., $k$ must take on values 0,1,2, and so on. This is very important, because if you wanted to model a population you could not make sense of populations with 4.25 or 5.612 members. \n", "\n", "If a random variable $Z$ has a Poisson mass distribution, we denote this by writing\n", "\n", "$$Z \\sim \\text{Poi}(\\lambda) $$\n", "\n", "One useful property of the <PERSON><PERSON><PERSON> distribution is that its expected value is equal to its parameter, i.e.:\n", "\n", "$$E\\large[ \\;Z\\; | \\; \\lambda \\;\\large] = \\lambda $$\n", "\n", "We will use this property often, so it's useful to remember. Below, we plot the probability mass distribution for different $\\lambda$ values. The first thing to notice is that by increasing $\\lambda$, we add more probability of larger values occurring. Second, notice that although the graph ends at 15, the distributions do not. They assign positive probability to every non-negative integer."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAvgAAAEfCAYAAAA0i7JEAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nwAcAgIn8AmR7R+/mpLe2AAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53c616fa58>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 4)\n", "\n", "import scipy.stats as stats\n", "a = np.arange(16)\n", "poi = stats.poisson\n", "lambda_ = [1.5, 4.25]\n", "colours = [\"#348ABD\", \"#A60628\"]\n", "\n", "plt.bar(a, poi.pmf(a, lambda_[0]), color=colours[0],\n", "        label=\"$\\lambda = %.1f$\" % lambda_[0], alpha=0.60,\n", "        edgecolor=colours[0], lw=\"3\")\n", "\n", "plt.bar(a, poi.pmf(a, lambda_[1]), color=colours[1],\n", "        label=\"$\\lambda = %.1f$\" % lambda_[1], alpha=0.60,\n", "        edgecolor=colours[1], lw=\"3\")\n", "\n", "plt.xticks(a + 0.4, a)\n", "plt.legend()\n", "plt.ylabel(\"probability of $k$\")\n", "plt.xlabel(\"$k$\")\n", "plt.title(\"Probability mass function of a Poisson random variable; differing \\\n", "$\\lambda$ values\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Continuous Case\n", "Instead of a probability mass function, a continuous random variable has a *probability density function*. This might seem like unnecessary nomenclature, but the density function and the mass function are very different creatures. An example of continuous random variable is a random variable with *exponential density*. The density function for an exponential random variable looks like this:\n", "\n", "$$f_Z(z | \\lambda) = \\lambda e^{-\\lambda z }, \\;\\; z\\ge 0$$\n", "\n", "Like a Poisson random variable, an exponential random variable can take on only non-negative values. But unlike a Poisson variable, the exponential can take on *any* non-negative values, including non-integral values such as 4.25 or 5.612401. This property makes it a poor choice for count data, which must be an integer, but a great choice for time data, temperature data (measured in Kelvins, of course), or any other precise *and positive* variable. The graph below shows two probability density functions with different $\\lambda$ values. \n", "\n", "When a random variable $Z$ has an exponential distribution with parameter $\\lambda$, we say *$Z$ is exponential* and write\n", "\n", "$$Z \\sim \\text{Exp}(\\lambda)$$\n", "\n", "Given a specific $\\lambda$, the expected value of an exponential random variable is equal to the inverse of $\\lambda$, that is:\n", "\n", "$$E[\\; Z \\;|\\; \\lambda \\;] = \\frac{1}{\\lambda}$$"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAvUAAAEfCAYAAADBdTn0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nBvVERERERDn3/yYiCPS0Jy4rAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53c6873cc0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a = np.linspace(0, 4, 100)\n", "expo = stats.expon\n", "lambda_ = [0.5, 1]\n", "\n", "for l, c in zip(lambda_, colours):\n", "    plt.plot(a, expo.pdf(a, scale=1./l), lw=3,\n", "             color=c, label=\"$\\lambda = %.1f$\" % l)\n", "    plt.fill_between(a, expo.pdf(a, scale=1./l), color=c, alpha=.33)\n", "\n", "plt.legend()\n", "plt.ylabel(\"PDF at $z$\")\n", "plt.xlabel(\"$z$\")\n", "plt.ylim(0,1.2)\n", "plt.title(\"Probability density function of an Exponential random variable;\\\n", " differing $\\lambda$\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### But what is $\\lambda \\;$?\n", "\n", "\n", "**This question is what motivates statistics**. In the real world, $\\lambda$ is hidden from us. We see only $Z$, and must go backwards to try and determine $\\lambda$. The problem is difficult because there is no one-to-one mapping from $Z$ to $\\lambda$. Many different methods have been created to solve the problem of estimating $\\lambda$, but since $\\lambda$ is never actually observed, no one can say for certain which method is best! \n", "\n", "Bayesian inference is concerned with *beliefs* about what $\\lambda$ might be. Rather than try to guess $\\lambda$ exactly, we can only talk about what $\\lambda$ is likely to be by assigning a probability distribution to $\\lambda$.\n", "\n", "This might seem odd at first. After all, $\\lambda$ is fixed; it is not (necessarily) random! How can we assign probabilities to values of a non-random variable? Ah, we have fallen for our old, frequentist way of thinking. Recall that under Bayesian philosophy, we *can* assign probabilities if we interpret them as beliefs. And it is entirely acceptable to have *beliefs* about the parameter $\\lambda$. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "##### Example: Inferring behaviour from text-message data\n", "\n", "Let's try to model a more interesting example, one that concerns the rate at which a user sends and receives text messages:\n", "\n", ">  You are given a series of daily text-message counts from a user of your system. The data, plotted over time, appears in the chart below. You are curious to know if the user's text-messaging habits have changed over time, either gradually or suddenly. How can you model this? (This is in fact my own text-message data. Judge my popularity as you wish.)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAuoAAAEBCAYAAAAuDIB6AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53c60622e8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 3.5)\n", "count_data = np.loadtxt(\"data/txtdata.csv\")\n", "n_count_data = len(count_data)\n", "plt.bar(np.arange(n_count_data), count_data, color=\"#348ABD\")\n", "plt.xlabel(\"Time (days)\")\n", "plt.ylabel(\"count of text-msgs received\")\n", "plt.title(\"Did the user's texting habits change over time?\")\n", "plt.xlim(0, n_count_data);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before we start modeling, see what you can figure out just by looking at the chart above. Would you say there was a change in behaviour during this time period? \n", "\n", "How can we start to model this? Well, as we have conveniently already seen, a Poisson random variable is a very appropriate model for this type of *count* data. Denoting day $i$'s text-message count by $C_i$, \n", "\n", "$$ C_i \\sim \\text{<PERSON><PERSON>on}(\\lambda)  $$\n", "\n", "We are not sure what the value of the $\\lambda$ parameter really is, however. Looking at the chart above, it appears that the rate might become higher late in the observation period, which is equivalent to saying that $\\lambda$ increases at some point during the observations. (Recall that a higher value of $\\lambda$ assigns more probability to larger outcomes. That is, there is a higher probability of many text messages having been sent on a given day.)\n", "\n", "How can we represent this observation mathematically? Let's assume that on some day during the observation period (call it $\\tau$), the parameter $\\lambda$ suddenly jumps to a higher value. So we really have two $\\lambda$ parameters: one for the period before $\\tau$, and one for the rest of the observation period. In the literature, a sudden transition like this would be called a *switchpoint*:\n", "\n", "$$\n", "\\lambda = \n", "\\begin{cases}\n", "\\lambda_1  & \\text{if } t \\lt \\tau \\cr\n", "\\lambda_2 & \\text{if } t \\ge \\tau\n", "\\end{cases}\n", "$$\n", "\n", "\n", "If, in reality, no sudden change occurred and indeed $\\lambda_1 = \\lambda_2$, then the $\\lambda$s posterior distributions should look about equal.\n", "\n", "We are interested in inferring the unknown $\\lambda$s. To use Bayesian inference, we need to assign prior probabilities to the different possible values of $\\lambda$. What would be good prior probability distributions for $\\lambda_1$ and $\\lambda_2$? Recall that $\\lambda$ can be any positive number. As we saw earlier, the *exponential* distribution provides a continuous density function for positive numbers, so it might be a good choice for modeling $\\lambda_i$. But recall that the exponential distribution takes a parameter of its own, so we'll need to include that parameter in our model. Let's call that parameter $\\alpha$.\n", "\n", "\\begin{align}\n", "&\\lambda_1 \\sim \\text{Exp}( \\alpha ) \\\\\\\n", "&\\lambda_2 \\sim \\text{Exp}( \\alpha )\n", "\\end{align}\n", "\n", "$\\alpha$ is called a *hyper-parameter* or *parent variable*. In literal terms, it is a parameter that influences other parameters. Our initial guess at $\\alpha$ does not influence the model too strongly, so we have some flexibility in our choice.  A good rule of thumb is to set the exponential parameter equal to the inverse of the average of the count data. Since we're modeling $\\lambda$ using an exponential distribution, we can use the expected value identity shown earlier to get:\n", "\n", "$$\\frac{1}{N}\\sum_{i=0}^N \\;C_i \\approx E[\\; \\lambda \\; |\\; \\alpha ] = \\frac{1}{\\alpha}$$ \n", "\n", "An alternative, and something I encourage the reader to try, would be to have two priors: one for each $\\lambda_i$. Creating two exponential distributions with different $\\alpha$ values reflects our prior belief that the rate changed at some point during the observations.\n", "\n", "What about $\\tau$? Because of the noisiness of the data, it's difficult to pick out a priori when $\\tau$ might have occurred. Instead, we can assign a *uniform prior belief* to every possible day. This is equivalent to saying\n", "\n", "\\begin{align}\n", "& \\tau \\sim \\text{DiscreteUniform(1,70) }\\\\\\\\\n", "& \\Rightarrow P( \\tau = k ) = \\frac{1}{70}\n", "\\end{align}\n", "\n", "So after all this, what does our overall prior distribution for the unknown variables look like? Frankly, *it doesn't matter*. What we should understand is that it's an ugly, complicated mess involving symbols only a mathematician could love. And things will only get uglier the more complicated our models become. Regardless, all we really care about is the posterior distribution.\n", "\n", "We next turn to PyMC3, a Python library for performing Bayesian analysis that is undaunted by the mathematical monster we have created. \n", "\n", "\n", "Introducing our first hammer: PyMC3\n", "-----\n", "\n", "PyMC3 is a Python library for programming Bayesian analysis [3]. It is a fast, well-maintained library. The only unfortunate part is that its documentation is lacking in certain areas, especially those that bridge the gap between beginner and hacker. One of this book's main goals is to solve that problem, and also to demonstrate why PyMC3 is so cool.\n", "\n", "We will model the problem above using PyMC3. This type of programming is called *probabilistic programming*, an unfortunate misnomer that invokes ideas of randomly-generated code and has likely confused and frightened users away from this field. The code is not random; it is probabilistic in the sense that we create probability models using programming variables as the model's components. Model components are first-class primitives within the PyMC3 framework. \n", "\n", "<PERSON><PERSON> [5] has a very motivating description of probabilistic programming:\n", "\n", ">   Another way of thinking about this: unlike a traditional program, which only runs in the forward directions, a probabilistic program is run in both the forward and backward direction. It runs forward to compute the consequences of the assumptions it contains about the world (i.e., the model space it represents), but it also runs backward from the data to constrain the possible explanations. In practice, many probabilistic programming systems will cleverly interleave these forward and backward operations to efficiently home in on the best explanations.\n", "\n", "Because of the confusion engendered by the term *probabilistic programming*, I'll refrain from using it. Instead, I'll simply say *programming*, since that's what it really is. \n", "\n", "PyMC3 code is easy to read. The only novel thing should be the syntax. Simply remember that we are representing the model's components ($\\tau, \\lambda_1, \\lambda_2$ ) as variables."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Applied log-transform to lambda_1 and added transformed lambda_1_log_ to model.\n", "Applied log-transform to lambda_2 and added transformed lambda_2_log_ to model.\n"]}], "source": ["import pymc3 as pm\n", "\n", "with pm.Model() as model:\n", "    alpha = 1.0/count_data.mean()  # Recall count_data is the\n", "                                   # variable that holds our txt counts\n", "    lambda_1 = pm.Exponential(\"lambda_1\", alpha)\n", "    lambda_2 = pm.Exponential(\"lambda_2\", alpha)\n", "    \n", "    tau = pm.DiscreteUniform(\"tau\", lower=0, upper=n_count_data - 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the code above, we create the PyMC3 variables corresponding to $\\lambda_1$ and $\\lambda_2$. We assign them to PyMC3's *stochastic variables*, so-called because they are treated by the back end as random number generators."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [], "source": ["with model:\n", "    idx = np.arange(n_count_data) # Index\n", "    lambda_ = pm.math.switch(tau > idx, lambda_1, lambda_2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This code creates a new function `lambda_`, but really we can think of it as a random variable: the random variable $\\lambda$ from above. The `switch()` function assigns `lambda_1` or `lambda_2` as the value of `lambda_`, depending on what side of `tau` we are on. The values of `lambda_` up until `tau` are `lambda_1` and the values afterwards are `lambda_2`.\n", "\n", "Note that because `lambda_1`, `lambda_2` and `tau` are random, `lambda_` will be random. We are **not** fixing any variables yet."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [], "source": ["with model:\n", "    observation = pm.Poisson(\"obs\", lambda_, observed=count_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The variable `observation` combines our data, `count_data`, with our proposed data-generation scheme, given by the variable `lambda_`, through the `observed` keyword. \n", "\n", "The code below will be explained in Chapter 3, but I show it here so you can see where our results come from. One can think of it as a *learning* step. The machinery being employed is called *Markov Chain Monte Carlo* (MCMC), which I also delay explaining until Chapter 3. This technique returns thousands of random variables from the posterior distributions of $\\lambda_1, \\lambda_2$ and $\\tau$. We can plot a histogram of the random variables to see what the posterior distributions look like. Below, we collect the samples (called *traces* in the MCMC literature) into histograms."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10000/10000 [00:02<00:00, 4511.50it/s]\n"]}], "source": ["### Mysterious code to be explained in Chapter 3.\n", "with model:\n", "    step = pm.Metropolis()\n", "    trace = pm.sample(10000, tune=5000, step=step, return_inferencedata=False)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [], "source": ["lambda_1_samples = trace['lambda_1']\n", "lambda_2_samples = trace['lambda_2']\n", "tau_samples = trace['tau']"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAvQAAAKACAYAAADzb+zBAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niEgd04ReRERERKSOaUIvIiIiIlLH/j+iN1eauGJfmQAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53abe52dd8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 10)\n", "#histogram of the samples:\n", "\n", "ax = plt.subplot(311)\n", "ax.set_autoscaley_on(False)\n", "\n", "plt.hist(lambda_1_samples, histtype='stepfilled', bins=30, alpha=0.85,\n", "         label=\"posterior of $\\lambda_1$\", color=\"#A60628\", density=True)\n", "plt.legend(loc=\"upper left\")\n", "plt.title(r\"\"\"Posterior distributions of the variables\n", "    $\\lambda_1,\\;\\lambda_2,\\;\\tau$\"\"\")\n", "plt.xlim([15, 30])\n", "plt.xlabel(\"$\\lambda_1$ value\")\n", "\n", "ax = plt.subplot(312)\n", "ax.set_autoscaley_on(False)\n", "plt.hist(lambda_2_samples, histtype='stepfilled', bins=30, alpha=0.85,\n", "         label=\"posterior of $\\lambda_2$\", color=\"#7A68A6\", density=True)\n", "plt.legend(loc=\"upper left\")\n", "plt.xlim([15, 30])\n", "plt.xlabel(\"$\\lambda_2$ value\")\n", "\n", "plt.subplot(313)\n", "w = 1.0 / tau_samples.shape[0] * np.ones_like(tau_samples)\n", "plt.hist(tau_samples, bins=n_count_data, alpha=1,\n", "         label=r\"posterior of $\\tau$\",\n", "         color=\"#467821\", weights=w, rwidth=2.)\n", "plt.xticks(np.arange(n_count_data))\n", "\n", "plt.legend(loc=\"upper left\")\n", "plt.ylim([0, .75])\n", "plt.xlim([35, len(count_data)-20])\n", "plt.xlabel(r\"$\\tau$ (in days)\")\n", "plt.ylabel(\"probability\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interpretation\n", "\n", "Recall that Bayesian methodology returns a *distribution*. Hence we now have distributions to describe the unknown $\\lambda$s and $\\tau$. What have we gained? Immediately, we can see the uncertainty in our estimates: the wider the distribution, the less certain our posterior belief should be. We can also see what the plausible values for the parameters are: $\\lambda_1$ is around 18 and $\\lambda_2$ is around 23. The posterior distributions of the two $\\lambda$s are clearly distinct, indicating that it is indeed likely that there was a change in the user's text-message behaviour.\n", "\n", "What other observations can you make? If you look at the original data again, do these results seem reasonable? \n", "\n", "Notice also that the posterior distributions for the $\\lambda$s do not look like exponential distributions, even though our priors for these variables were exponential. In fact, the posterior distributions are not really of any form that we recognize from the original model. But that's OK! This is one of the benefits of taking a computational point of view. If we had instead done this analysis using mathematical approaches, we would have been stuck with an analytically intractable (and messy) distribution. Our use of a computational approach makes us indifferent to mathematical tractability.\n", "\n", "Our analysis also returned a distribution for $\\tau$. Its posterior distribution looks a little different from the other two because it is a discrete random variable, so it doesn't assign probabilities to intervals. We can see that near day 45, there was a 50% chance that the user's behaviour changed. Had no change occurred, or had the change been gradual over time, the posterior distribution of $\\tau$ would have been more spread out, reflecting that many days were plausible candidates for $\\tau$. By contrast, in the actual results we see that only three or four days make any sense as potential transition points. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Why would I want samples from the posterior, anyways?\n", "\n", "\n", "We will deal with this question for the remainder of the book, and it is an understatement to say that it will lead us to some amazing results. For now, let's end this chapter with one more example.\n", "\n", "We'll use the posterior samples to answer the following question: what is the expected number of texts at day $t, \\; 0 \\le t \\le 70$ ? Recall that the expected value of a Poisson variable is equal to its parameter $\\lambda$. Therefore, the question is equivalent to *what is the expected value of $\\lambda$ at time $t$*?\n", "\n", "In the code below, let $i$ index samples from the posterior distributions. Given a day $t$, we average over all possible $\\lambda_i$ for that day $t$, using $\\lambda_i = \\lambda_{1,i}$ if $t \\lt \\tau_i$ (that is, if the behaviour change has not yet occurred), else we use $\\lambda_i = \\lambda_{2,i}$. "]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAuoAAAFVCAYAAAC5E8qbAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niEgFaaAuIiIiIlJBGqiLiIiIiFTQ/wcvO0//8pTK2wAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f53ab6616d8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figsize(12.5, 5)\n", "# tau_samples, lambda_1_samples, lambda_2_samples contain\n", "# N samples from the corresponding posterior distribution\n", "N = tau_samples.shape[0]\n", "expected_texts_per_day = np.zeros(n_count_data)\n", "for day in range(0, n_count_data):\n", "    # ix is a bool index of all tau samples corresponding to\n", "    # the switchpoint occurring prior to value of 'day'\n", "    ix = day < tau_samples\n", "    # Each posterior sample corresponds to a value for tau.\n", "    # for each day, that value of tau indicates whether we're \"before\"\n", "    # (in the lambda1 \"regime\") or\n", "    #  \"after\" (in the lambda2 \"regime\") the switchpoint.\n", "    # by taking the posterior sample of lambda1/2 accordingly, we can average\n", "    # over all samples to get an expected value for lambda on that day.\n", "    # As explained, the \"message count\" random variable is Poisson distributed,\n", "    # and therefore lambda (the poisson parameter) is the expected value of\n", "    # \"message count\".\n", "    expected_texts_per_day[day] = (lambda_1_samples[ix].sum()\n", "                                   + lambda_2_samples[~ix].sum()) / N\n", "\n", "\n", "plt.plot(range(n_count_data), expected_texts_per_day, lw=4, color=\"#E24A33\",\n", "         label=\"expected number of text-messages received\")\n", "plt.xlim(0, n_count_data)\n", "plt.xlabel(\"Day\")\n", "plt.ylabel(\"Expected # text-messages\")\n", "plt.title(\"Expected number of text-messages received\")\n", "plt.ylim(0, 60)\n", "plt.bar(np.arange(len(count_data)), count_data, color=\"#348ABD\", alpha=0.65,\n", "        label=\"observed texts per day\")\n", "\n", "plt.legend(loc=\"upper left\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our analysis shows strong support for believing the user's behavior did change ($\\lambda_1$ would have been close in value to $\\lambda_2$ had this not been true), and that the change was sudden rather than gradual (as demonstrated by $\\tau$'s strongly peaked posterior distribution). We can speculate what might have caused this: a cheaper text-message rate, a recent weather-to-text subscription, or perhaps a new relationship. (In fact, the 45th day corresponds to Christmas, and I moved away to Toronto the next month, leaving a girlfriend behind.)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Exercises\n", "\n", "1\\.  Using `lambda_1_samples` and `lambda_2_samples`, what is the mean of the posterior distributions of $\\lambda_1$ and $\\lambda_2$?"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": false}, "outputs": [], "source": ["#type your code here."]}, {"cell_type": "markdown", "metadata": {}, "source": ["2\\.  What is the expected percentage increase in text-message rates? `hint:` compute the mean of `lambda_1_samples/lambda_2_samples`. Note that this quantity is very different from `lambda_1_samples.mean()/lambda_2_samples.mean()`."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": false}, "outputs": [], "source": ["#type your code here."]}, {"cell_type": "markdown", "metadata": {}, "source": ["3\\. What is the mean of $\\lambda_1$ **given** that we know $\\tau$ is less than 45.  That is, suppose we have been given new information that the change in behaviour occurred prior to day 45. What is the expected value of $\\lambda_1$ now? (You do not need to redo the PyMC3 part. Just consider all instances where `tau_samples < 45`.)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": false}, "outputs": [], "source": ["#type your code here."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### References\n", "\n", "\n", "-  [1] <PERSON><PERSON><PERSON>, Andrew. N.p.. Web. 22 Jan 2013. [N is never large enough](http://andrewgelman.com/2005/07/31/n_is_never_larg).\n", "-  [2] <PERSON><PERSON><PERSON>, <PERSON>. 2009. [The Unreasonable Effectiveness of Data](http://static.googleusercontent.com/media/research.google.com/en//pubs/archive/35179.pdf).\n", "- [3] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and Fon<PERSON>beck C. (2016) Probabilistic programming in Python using PyMC3. *PeerJ Computer Science* 2:e55 <https://doi.org/10.7717/peerj-cs.55>\n", "- [4] <PERSON> and <PERSON><PERSON>. Large-Scale Machine Learning at Twitter. Proceedings of the 2012 ACM SIGMOD International Conference on Management of Data (SIGMOD 2012), pages 793-804, May 2012, Scottsdale, Arizona.\n", "- [5] <PERSON><PERSON><PERSON>, <PERSON>. \"Why Probabilistic Programming Matters.\" 24 Mar 2013. Google, Online Posting to Google . Web. 24 Mar. 2013. <https://plus.google.com/u/0/107971134877020469960/posts/KpeRdJKR6Z1>."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<style>\n", "    @font-face {\n", "        font-family: \"Computer Modern\";\n", "        src: url('http://9dbb143991406a7c655e-aa5fcb0a5a4ec34cff238a2d56ca4144.r56.cf5.rackcdn.com/cmunss.otf');\n", "    }\n", "    div.cell{\n", "        width:800px;\n", "        margin-left:16% !important;\n", "        margin-right:auto;\n", "    }\n", "    h1 {\n", "        font-family: Helvetica, serif;\n", "    }\n", "    h4{\n", "        margin-top:12px;\n", "        margin-bottom: 3px;\n", "       }\n", "    div.text_cell_render{\n", "        font-family: Computer Modern, \"Helvetica Neue\", Arial, Helvetica, Geneva, sans-serif;\n", "        line-height: 145%;\n", "        font-size: 130%;\n", "        width:800px;\n", "        margin-left:auto;\n", "        margin-right:auto;\n", "    }\n", "    .CodeMirror{\n", "            font-family: \"Source Code Pro\", source-code-pro,Consolas, monospace;\n", "    }\n", "    .prompt{\n", "        display: None;\n", "    }\n", "    .text_cell_render h5 {\n", "        font-weight: 300;\n", "        font-size: 22pt;\n", "        color: #4057A1;\n", "        font-style: italic;\n", "        margin-bottom: .5em;\n", "        margin-top: 0.5em;\n", "        display: block;\n", "    }\n", "    \n", "    .warning{\n", "        color: rgb( 240, 20, 20 )\n", "        }  \n", "</style>\n"], "text/plain": ["<IPython.core.display.HTML at 0x10f034850>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.core.display import HTML\n", "def css_styling():\n", "    styles = open(\"../styles/custom.css\", \"r\").read()\n", "    return HTML(styles)\n", "css_styling()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "<PERSON> [bayes]", "language": "python", "name": "<PERSON> [bayes]"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 0}