document.body.style.backgroundColor = 'black';

const topWin = window.top || window;
attachManifest();


const { default: ODANT } = await import('/web/core/odant.js');

let wallpaper = 'bubbles'
let wp_path = `/web/oda/tools/styles/wallpapers/${wallpaper}/${wallpaper}.js`;
import(wp_path).then(res => {
    if (!window.wallpaper) {
        window.wallpaper = document.createElement(`oda-` + wallpaper);
        document.body.appendChild(window.wallpaper);
    }
});
ODANT.ssid ??= (new URL(document.baseURI)).searchParams.get('ssid');
const { getLoader } = await ODA.import('@oda/loader');
ODANT.loader = await getLoader('odant');
ODANT.tasks = ODANT.loader.tasks;
ODANT.loader.addTask(ODANT);

// await installServiceWorker();

if (ODANT.IsReady) {
    await start();
} else {
    globalThis.addEventListener('odant-ready', start);
}


async function installServiceWorker() {
    if ('serviceWorker' in navigator && location.protocol === 'https:') {
        const { getLoader } = await ODA.import('@oda/loader');
        ODANT.loader = await getLoader('odant');
        ODANT.tasks = ODANT.loader.tasks;
        try {
            navigator.serviceWorker.register('/@@task-sw');
            await new Promise(async (resolve, reject) => {
                const id = setTimeout(() => {
                    reject(new Error('The worker registration took too long'));
                }, 10000);
                const res = await navigator.serviceWorker.ready;
                clearTimeout(id);
                resolve(res);
            });
            navigator.serviceWorker.addEventListener('message', messageHandler, false);
            window.addEventListener('message', messageHandler, false);
            const tasksToRemove = [];
            function messageHandler(e) {
                if (!['task-start', 'task-end', 'task-error'].includes(e.data?.action)) return;
                if (window !== window.top) {
                    e.data.retranslated = true;
                    return topWin.postMessage(e.data);
                }
                const data = e.data.data;
                switch (e.data.action) {
                    case 'task-start': {
                        ODANT.loader.addTask(data);
                    } break;
                    case 'task-end': {
                        tasksToRemove.push(data);
                        ODANT.loader.debounce('tasks-updating', () => {
                            const tasks = tasksToRemove.splice(0, tasksToRemove.length);
                            tasks.forEach(t => ODANT.loader.removeTask(t));
                        }, 1000);
                    } break;
                }
            }
        }
        catch (err) {
            console.error(err);
        }
    }
}

function attachManifest() {
    const manifestLink = document.createElement('link');
    manifestLink.setAttribute('rel', 'manifest');
    const short_name = document.title.slice(0, document.title.indexOf('[') - 1);
    manifestLink.setAttribute('href', `./manifest.json?name=${document.title}&short_name=${short_name}`);
    manifestLink.setAttribute('crossorigin', 'use-credentials');
    document.head.appendChild(manifestLink);
}
async function start() {
    globalThis.removeEventListener('core-ready', start);
    const context = CORE.getContextFromUrl(decodeURI(location.pathname));
    const item = await (async () => {
        try {
            const res = context ? await CORE.host.findItem(context) : CORE.host;
            if (!res) {
                throw new Error('Контекст не был получен!');
            }
            return res;
        }
        catch (err) {
            console.error(err);
            return CORE.host;
        }
    })();
    document.title = document.title.replace("{{item-label}}", item?.label || '');
    let el;
    try {
        const module = await import(`/api/${context}/~/client/pages/${window.mainComponentName}/${window.mainComponentName}.js`);
        let path = location.pathname.split('#')[0].split('/');
        path.pop();
        let p = [];
        let s;
        while (s = path.pop()) {
            if (s === 'pages') break;
            p.unshift(s);
        }
        const handlerName = p.join('/');
        const handler = await item.getHandler(handlerName);
        await ODANT.tryReg(handler.tagName, item?.path || context);
        el = document.createElement(handler.tagName);

        const Auth = await import('/web/auth/auth.js');
        const security = Auth.getSecurity();
        if (!security.login) {
            try {
                await CORE.host.executeHandler('authorization');
            }
            catch (err) {
                console.log('Auth as guest');
            }
        }


        document.body.insertBefore(el, document.body.firstChild);
        el.context = context;

        const id = setInterval(async () => {
            if (item.path && customElements.get(handler.tagName)) {
                el.contextItem = item;
                el.$handler = handler;
                clearInterval(id);
            }
        }, 10);
    }
    catch (e) {
        console.warn(e);
        const handler = await item.getHandler(window.mainComponentName);
        if (!handler)
            throw new Error(`Action "${window.mainComponentName}" not found for ${item.path}!`);
    }
    finally {
        ODANT.loader.removeTask(ODANT)
        if (window.wallpaper)
            window.wallpaper.remove();
        else
            window.wallpaper = {};
        document.body.style.backgroundColor = '';
    }
};
export { };