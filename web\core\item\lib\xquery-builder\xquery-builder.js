ODANT({ is: 'odant-xquery-builder', imports: '@lib/xml-tree',
    template: /*html*/`
        <style>
            :host {
                @apply --vertical;
            }
        </style>
        <odant-xquery-builder-sources ~if="!hideSourcePanel" slot="left-panel" opened label="Sources"></odant-xquery-builder-sources>
        <odant-xml-tree slot="right-panel" allow-drag label="XML tree" :context-item="focusedSourceItem" order="2"></odant-xml-tree>
        <odant-xquery-builder-toolbar slot="top"></odant-xquery-builder-toolbar>
        <odant-xquery-builder-editor></odant-xquery-builder-editor>
    `,
    hideSourcePanel: false,
    $pdp: {
        _inRun: false,
        hideContextItemInSources: false,
        $handler: Object,
        _sourceOrXQChanged: true,
        viewMode: {
            $def: 'XQuery',
            $list: ['XQuery', 'Source']
        },
        resultType: {
            $def: '',
            $list: ['XML', 'JSON', 'DataSet']
        },
        // get iconPlay() {
        //     return this._inRun ? 'av:stop' : 'av:play-arrow';
        // },
        settingsObject: {
            $type: Object,
            get() {
                return new SettingsObject(this.$handler?.root || this.data);
            }
        },
        items: {
            $type: Array,
            get() {
                const items = this.settingsObject?.items?.map(i => {
                    return this.contextItem.findItem(i.path);
                });
                return Promise.all(items).then(items => items.filter(i => i !== this.contextItem));
            }
        },
        focusedSourceItem: {
            $type: Object,
            // async get() {
            //     return this.contextItem;
            // },
            set(n, o) {
                this._sourceOrXQChanged = true;
                this.clearResults();

                const h = async e => {
                    this._sourceOrXQChanged = true;
                }
                if (o)
                    o.unlisten('changed', h);
                if (n)
                    n.listen('changed', h);
            }
        },
        sourceElement: {
            $type: Object,
            get() {
                if (this.focusedSourceItem)
                    return this.settingsObject.items?.find(i => i.path === this.focusedSourceItem.path) || null;
                return null;
            }
        },
        xquery: {
            $def: '*',
            get() {
                const xq = this.sourceElement?.xquery;
                return xq ? decodeURIComponent(xq) : '*';
            },
            set(n) {
                if (this.sourceElement) {
                    this.sourceElement.xquery = encodeURIComponent(n);
                }

                this._sourceOrXQChanged = true;
            }
        },
        xqueryPrefix: '',
        isSource: {
            $def: false,
            set(n) {
                if (n) {
                    this.run();
                }
            }
        },
        xml: '',
        json: Object,
        jsonString: {
            $def: '',
            get() {
                return this.json && JSON.stringify(this.json, null, 2) || '';
            }
        },
        dataSetData: {
            $def: [],
            get() {
                return this.json?.$DATASET?.[0]?.$ROWDATA?.[0]?.$ROW || [];
            }
        },
        dataSetColumns: {
            $def: [],
            get() {
                let fields = this.json?.$DATASET?.[0]?.$METADATA?.[0]?.$FIELD || [];
                fields = fields.map(f => ({
                    name: f.name || f.Name,
                    label: f.label || f.Label,
                    type: f.type || f.Type,
                    readOnly: true
                }));
                return fields;
            }
        },
        dataSetFieldsStructure: {
            $def: {},
            get() {
                let fields = this.json?.$DATASET?.[0]?.$METADATA?.[0]?.$FIELD || [];
                fields = fields.map((f => ({ n: f.name || f.Name })));
                return {
                    "$DOC": [
                        {
                            "c": 1,
                            "$A": fields
                        }
                    ]
                }
            }
        },
    },
    $observers: {
        _initFocusedSourceItem: 'hideContextItemInSources, contextItem'
    },
    async _initFocusedSourceItem(hide = this.hideContextItemInSources, contextItem = this.contextItem) {
        if (hide) {
            const items = await this.items;
            if (items.length) {
                this.focusedSourceItem = items[0];
            }
            else {
                do {
                    try {
                        await this.addSource();
                    } catch (err) {

                    }
                } while (!this.focusedSourceItem)
            }
        }
        else {
            this.focusedSourceItem = contextItem;
        }
    },
    async addSource(e) {
        const result = await this.$handler.showDataSourceDialog();
        const { name, label, path, type = 'ODANT' } = result;
        this.settingsObject?.addSource({ name, label, path, type });
        this.items = undefined;
        this.focusedSourceItem = result;
    },
    async delSource(e) {
        await this.contextItem.showConfirm('Delete data source?');
        this.settingsObject?.delSource(this.focusedSourceItem);
        this._initFocusedSourceItem();
    },
    clearResults() {
        this.xml = '';
        this.json = null;
    },
    async run(run) {
        this._inRun = run;

        this.clearResults();

        let res;
        let src = this.focusedSourceItem;
        let mask = '*';
        let xq = this.isSource ? '*' : `${this.xqueryPrefix}(${this.xquery})`;
        let format = this.resultType === 'XML' ? 'xml' : 'json';

        if (src instanceof odaFile) {
            const params = Object.assign({}, this.sourceElement, { mask, format, xquery: true });
            ['name', 'label', 'path', '$METADATA'].forEach(a => delete params[a]);
            res ||= await src.executeServerHandler('to-xml', params, xq);
        }
        else {
            res ||= await src.XQuery(xq, Object.assign(this.sourceElement || {}, { mask, format }));
        }

        if (format === 'xml') {
            this.xml = res || '';
        }
        else {
            this.json = res || {};
        }

        this._inRun = false;
        this._sourceOrXQChanged = false;
    },
    async showQueryWizard() {
        const { control } = await this.focusedSourceItem.showDialog('odant-xquery-wizard',
            { order: 2, allowCheck: 'single', disableElements: true }
        );
        const fields = new Set();
        const transformToXQ = (row, top = false) => {
            if (row.label.startsWith('@')) {
                // атрибут
                if ((row.checked === 'checked') || (row.checked === true)) {
                    const attrName = row.label.slice(1);
                    let name = attrName;
                    let value = `@${attrName}`;
                    let i = 1;
                    while (fields.has(name)) {
                        name = `${attrName}${i++}`;
                    }
                    fields.add(name);
                    return { attrs: [{ name, value }] };
                }
            }
            else {
                // элемент
                const children = row.items?.map(transformToXQ).flat().filter(Boolean);
                if (!children?.length)
                    return;

                const hasPath = [];
                const noPath = [];
                children.forEach(a => {
                    if (!a?.attrs?.length)
                        return;

                    if ('path' in a) {
                        hasPath.push(a);
                    }
                    else {
                        noPath.push(...a.attrs);
                    }
                });

                const rowName = row.label.replace('<', '').replace('>', '');
                if (hasPath.length) {
                    // вложенная таблица
                    hasPath.forEach(a => {
                        a.deep++;
                        a.path = `${rowName}/${a.path}`;
                        a.attrs.push(...noPath.map(i => ({ name: i.name, value: `${'../'.repeat(a.deep)}${i.value}` })))
                    });
                    return hasPath;
                }
                else if (noPath.length) {
                    if ((row.count !== '1') || top) {
                        // таблица
                        return {
                            path: rowName,
                            deep: 0,
                            attrs: noPath
                        }
                    }
                    else {
                        // структура
                        return {
                            attrs: noPath.map(a => ({ name: a.name, value: `${rowName}/${a.value}` }))
                        }
                    }
                }
            }
        };

        const result = control.dataSet.map(r => transformToXQ(r, true)).flat().filter(Boolean);
        const meta = Array.from(fields).map(f => `\t\t<FIELD Name="${f}"/>`).join('\n');
        const attributes = result.map(a => {
            const attributes = a.attrs.map(r => `\t\t\t\tattribute ${r.name} { ${r.value} }`).join(',\n');
            return `\
        for $a in ${a.path}
        return element ROW
        {
            $a/(
${attributes}
            )
        }`;
        }).join(',\n');

        this.xquery = `\
<DATASET>
    <METADATA>
${meta.trimEnd()}
    </METADATA>
    <ROWDATA>
    {
${attributes}
    }
    </ROWDATA>
</DATASET>`;
    },
    data: {},
    $public: {
        $pdp: true,
        iconSize: 24,
        wrap: {
            $def: true,
            $save: true
        },
        fontSize: 16,
    }
})

ODA({ is: 'odant-xquery-builder-sources', imports: '@oda/button',
    template: /*html*/`
        <style>
            :host {
                @apply --vertical;
            }
            odant-item-node {
                padding: 4px;
            }
        </style>
        <div class="horizontal header" style="justify-content: space-between">
            <oda-button :icon-size icon="icons:add" @tap="addSource">Add query source</oda-button>
            <oda-button :icon-size :disabled="!focusedSourceItem" icon="icons:delete" @tap="delSource"></oda-button>
        </div>
        <odant-item-node ~if="!hideContextItemInSources" class="no-flex dark" show-type-label :focused="isContextItemFocused"  @pointerdown="_pointerdown()" :context-item></odant-item-node>
        <odant-source-item-node :focused="$for.item === focusedSourceItem" ~for="items" :item="$for.item" @pointerdown="_pointerdown($for.item)"></odant-source-item-node>
    `,
    get isContextItemFocused() {
        if (!this.contextItem || !this.sourceElement)
            return false;
        return this.contextItem.path === this.sourceElement.path;
    },
    _pointerdown(item = this.contextItem) {
        this.focusedSourceItem = item;
        this.sourceElement = this.settingsObject.items?.find(i => i.path === item.path) || null;
    }
})

ODA({ is: 'odant-source-item-node', imports: '@oda/button, @tools/property-grid',
    template: /*html*/`
        <style>
            :host {
                @apply --horizontal;
            }
            odant-item-node {
                margin-left: 24px;
                padding: 4px;
            }
        </style>
        <odant-item-node class="flex" show-type-label :focused :item :context-item="item"></odant-item-node>
        <oda-button
            ~if="showSettings"
            :icon-size
            icon="icons:settings-applications"
            placeholder="Settings"
            @pointerdown.stop="openSettings"
        ></oda-button>
    `,
    focused: false,
    item: {
        $type: Object
    },
    get showSettings() {
        return this.item?.ext === 'csv';
    },
    async openSettings() {
        // todo: костыль для CSV
        const currentSettings = this.settingsObject.items?.find(i => i.path === this.item.path) || null;
        const methodParams = new MethodParams(currentSettings);
        await this.item.showDialog('oda-property-grid', { inspectedObject: methodParams });
    }
})

class MethodParams extends ROCKS({
    $public: {
        'first-row-as-names': {
            $type: Boolean,
            get() {
                return this.draft['first-row-as-names'] || true;
            },
            set(v) {
                this.draft['first-row-as-names'] = v;
            }
        },
        'rows-splitter': {
            $type: String,
            get() {
                return this.draft['rows-splitter'] && decodeURIComponent(this.draft['rows-splitter']) || '\\n';
            },
            set(v) {
                this.draft['rows-splitter'] = encodeURIComponent(v);
            }
        },
        'columns-splitter': {
            $type: String,
            $list: [';', ','],
            get() {
                return this.draft['columns-splitter'] && decodeURIComponent(this.draft['columns-splitter']) || ';';
            },
            set(v) {
                this.draft['columns-splitter'] = encodeURIComponent(v);
            }
        }
    }
}) {
    constructor(draft) {
        super();
        this.draft = draft;
    }
}

ODA({ is: 'odant-xquery-builder-toolbar', imports: '@lib/item-node',
    template: /*html*/`
        <style>
            :host {
                @apply --horizontal;
                @apply --header;
                @apply --dark;
            }
            oda-button {
                padding: 4px;
            }
            [focused] {
                @apply --content;
            }

        </style>
        <div class="horizontal flex">
            <oda-button
                label="XQuery"
                icon="bootstrap:file-text"
                :focused="viewMode === 'XQuery'"
                @tap="viewMode = 'XQuery'"
            ></oda-button>
            <!--<oda-button @tap="run(true)" success-invert :icon="iconPlay"><span style="padding-right: 8px;">XQuery run</span></oda-button>-->
            <!--<oda-button :icon-size icon="bootstrap:magic" title="query wizard" @tap="showQueryWizard"></oda-button>
            <div class="flex"></div>-->
            <odant-item-node
                style="padding: 8px;"
                :context-item="focusedSourceItem"
                :focused="viewMode === 'Source'"
                show-type-label
                @tap="viewMode = 'Source'"
            ></odant-item-node>
        </div>
    `,
})

ODANT({ is: 'odant-xquery-wizard', extends: 'odant-xml-tree', imports: '@lib/xml-tree',
})



ODA({ is: 'odant-xquery-builder-editor', imports: '@oda/code-editor, @oda/splitter',
    template: /*html*/`
        <style>
            :host {
                @apply --vertical;
                overflow: hidden;
                @apply --flex;
            }
        </style>
        <oda-code-editor
            ~show="viewMode === 'XQuery'"
            class="flex"
            mode="xquery"
            :value="xquery"
            highlight-active-line="false"
            :wrap
            show-print-margin="false"
            :font-size
            @value-changed="_valueChanged"
            @dragover="_dragover"
        ></oda-code-editor>
        <oda-xml-editor ~show="viewMode === 'Source'" class="flex" :xml="focusedSourceItem?.xml" :wrap ></oda-xml-editor>
        <div class="vertical" ~show="viewMode === 'XQuery'" ~style="{ height: height + 'px' }">
            <oda-splitter ::height align="horizontal"></oda-splitter>
<!--            <oda-divider reverse use_px min="34" ~show="showResult" direction="horizontal" @end-splitter-move="_endSplitterMove"></oda-divider>-->
            <odant-xquery-builder-result style="overflow: hidden;"></odant-xquery-builder-result>
        </div>
    `,
    height: {
        $def: 200,
        $save: true
    },
    // $observers: {
    //     _viewModeChanged(viewMode) {
    //         const editor = this.$('oda-xml-editor');
    //         if (editor.editor)
    //             editor.setValue(editor.editor.getValue());
    //     }
    // },
    _valueChanged(e) {
        const value = e.detail.value;
        if (this.xquery !== value)
            this.xquery = value;
    },
    /** @param {DragEvent} e */
    _dragover(e) {
        e.preventDefault();
    },
    /** @param {DragEvent} e */
    // _drop(e) {
    //     if (e.dataTransfer.types.includes('odant/path')) {
    //         const text = getDropPath.call(this, e);
    //         const editor = e.currentTarget.editor;
    //         editor.session.insert(editor.getCursorPosition(), text);
    //         e.preventDefault();
    //     }
    // }
})

ODA({ is: 'odant-xquery-builder-result', imports: '@oda/xml-editor, @oda/table, @lib/xml-tree',
    template: /*html*/`
        <style>
            :host {
                @apply --vertical;
                @apply --flex;
            }
            oda-button[focused] {
                @apply --content;
            }
            oda-button[focused][is-green] {
                @apply --success-invert;
            }
        </style>
        <div class="horizontal header dark" ~show="!isSource">
            <div class="horizontal flex">
                <oda-button :icon="xmlIcon" :focused="resultType === 'XML'" :is-green="_sourceOrXQChanged" @tap="setResultTypeAndRun('XML')" label="XML"></oda-button>
                <oda-button :icon="jsonIcon" :focused="resultType === 'JSON'" :is-green="_sourceOrXQChanged" @tap="setResultTypeAndRun('JSON')" label="JSON"></oda-button>
                <oda-button :icon="dataSetIcon" :focused="resultType === 'DataSet'" :is-green="_sourceOrXQChanged" @tap="setResultTypeAndRun('DataSet')" label="DataSet"></oda-button>
            </div>
            <oda-button icon="editor:wrap-text" title="Wrap text" allow-toggle ::toggled="wrap"></oda-button>
        </div>
        <div class="flex vertical" style="overflow: hidden;">
            <div ~if="!resultType" class="flex" style="align-items: center; justify-content: center;" horizontal>
                <div class="dimmed">Choose xquery result type</div>
            </div>
            <oda-xml-editor class="flex" ~show="resultType === 'XML'" :xml :wrap :font-size style="height: 100%; overflow: auto; display: block;"></oda-xml-editor>
            <oda-code-editor ~show="resultType === 'JSON'" :mode="'json'" :value="jsonString" highlight-active-line="false" :wrap show-print-margin="false" :font-size style="height: 100%; overflow: auto; display: block;"></oda-code-editor>
            <div class="flex vertical" style="height: 100%;" ~show="resultType === 'DataSet'">
                <div ~if="!dataSetColumns?.length || !dataSetData?.length" class="flex vertical" style="align-items: center; justify-content: center; gap: 32px;">
                    <div class="dimmed">Query result doesn't match the data set format</div>
                    <oda-button label="Generate query via Wizard" class="no-flex raised" @tap="showQueryWizard" icon="bootstrap:magic"></oda-button>
                </div>
                <div class="flex horizontal" ~if="dataSetColumns?.length && dataSetData?.length" style="overflow: hidden;">
                    <oda-table flex show-header show-footer allow-sort even-odd row-lines col-lines
                        :columns="dataSetColumns" :data-set="dataSetData"></oda-table>
                    <div class="horizontal" ~style="{ width: width + 'px' }">
                        <oda-splitter ::width></oda-splitter>
                        <odant-xml-tree class="flex" :json-structure="dataSetFieldsStructure"></odant-xml-tree>
                    </div>
                </div>
            <div>
        </div>
    `,
    width: {
        $def: 200,
        $save: true
    },
    get xmlIcon() {
        return (this._inRun) && (this.resultType === 'XML')
               ? 'odant:spin'
               : 'bootstrap:filetype-xml'
    },
    get jsonIcon() {
        return (this._inRun) && (this.resultType === 'JSON')
               ? 'odant:spin'
               : 'bootstrap:filetype-json'
    },
    get dataSetIcon() {
        return (this._inRun) && (this.resultType === 'DataSet')
               ? 'odant:spin'
               : 'bootstrap:table'
    },
    // $observers: {
    //     setSource(focusedSourceItem) {
    //         this.run();
    //     }
    // },
    setResultTypeAndRun(newType) {
        this.resultType = newType;
        this.run(true);
    }
})

const SettingsObject = class extends ROCKS({
    root: null,
    get items() {
        return this.root?.$SOURCE;
    },
    get count() {
        return this.items?.length || 0;
    },
    addSource(source) {
        this.root.$SOURCE ??= [];
        const s = this.root.$SOURCE.find(s => s.path === source.path);
        if (s)
            Object.keys(source).forEach(i => (s[i] = source[i]));
        else
            this.root.$SOURCE.push(source);
        this.items = undefined;
        return source;
    },
    delSource(item) {
        const index = this.root.$SOURCE?.findIndex(s => s.path === item.path);
        if (~index) {
            this.root.$SOURCE?.splice(index, 1);
        }
    }
}) {
    constructor(root) {
        super();
        this.root = root;
    }
}

function getDropPath(e, context = this.contextItem) {
    e.preventDefault();

    const stringPath = e.dataTransfer.getData('odant/path');
    const jsonPath = JSON.parse(stringPath);
    let value = jsonPath[0];

    if (context instanceof odaClass) {
        context = context.$FIELDS;
    }

    let currentPath = context.path?.split('@')[0] || '';
    let targetPath = value.split('@')[0] || '';
    // console.log(currentPath, targetPath);
    if (value.startsWith('STATIC/') || value.startsWith('DETAILS/'))
        return '/' + value;
    if (value.startsWith('/CLASS/'))
        return value;
    let cur = currentPath.split('/');
    let target = targetPath.split('/');
    let min = Math.min(cur?.length, target?.length);
    let equal = '';
    for (let i = 0; i <= min; i++) {
        if (cur[i] && target[i] && cur[i] === target[i]) equal += cur[i] + '/';
        else break;
    }
    if (equal) {
        currentPath = currentPath.replace(equal, '');
        targetPath = targetPath.replace(equal, '');
        // console.log(equal, ' --- ' , currentPath, ' --- ', targetPath);
        value = value.replace(equal, '');
    }
    let steps = currentPath.split('/').length - 1;
    value = Array(steps).fill('../').join('') + value;
    return value;
}