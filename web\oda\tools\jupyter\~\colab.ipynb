{"cells": [{"cell_type": "code", "metadata": {"id": "0001"}, "source": ["for (var i = 0; i < 1325; i++) {\n", "    log(i)\n", "}"]}, {"cell_type": "html", "metadata": {"id": "0002"}, "source": ["<oda-palette size=10 gradients=10 colors=10></oda-palette>\n"]}, {"cell_type": "code", "metadata": {"id": "0003"}, "source": ["torus = await ODA.import('@apps/torus');\n", "print(torus.tensor.ones([4, 4]))\n\n", "ODA({ is: 'oda-ttt', imports: '@oda/button',\n", "   template: `\n", "       <div style=\"border: 1px solid red; margin: 8px;\">\n", "           bla-bla-bla\n", "           <oda-button icon=\"error\" icon-size=32></oda-button>\n", "       </div>\n", "   `\n", "})\n\n", "log(123)\n", "print(3333)\n", "log(777)\n", "\n", "warn('ewqrewrew')\n", "err('345345345', '888')\n", "return a=100\n"]}, {"cell_type": "html", "metadata": {"id": "0004"}, "source": ["<oda-ttt></oda-ttt>"]}, {"cell_type": "markdown", "metadata": {"id": "Wf5KrEb6vrkR"}, "source": ["# Level-1"]}, {"cell_type": "markdown", "metadata": {"id": "L-n9i3t70CBV"}, "source": ["## Level-2"]}, {"cell_type": "markdown", "metadata": {"id": "mQUsUpD30CBW"}, "source": ["*Content* 2.1\n", "\n", "...\n", "\n", "..."]}, {"cell_type": "markdown", "metadata": {"id": "89Lm6VBm0CBW"}, "source": ["Content 2.3\n", "\n", "...\n", "\n", "..."]}, {"cell_type": "markdown", "metadata": {"id": "ms47Cr7r0CBW"}, "source": ["## Level-2"]}, {"cell_type": "markdown", "metadata": {"id": "ZJhbWwRS0CBW"}, "source": ["Content level-2"]}, {"cell_type": "markdown", "source": ["Content level-2"], "metadata": {"id": "DuelE3Xn6Hzg"}}, {"cell_type": "markdown", "metadata": {"id": "RGRCz76C0CBW"}, "source": ["### Level-3"]}, {"cell_type": "markdown", "source": ["Content level-3"], "metadata": {"id": "Ieaa7I8w4i7E"}}, {"cell_type": "markdown", "source": ["Content level-3"], "metadata": {"id": "7e1-VJFk4ixs"}}, {"cell_type": "markdown", "source": ["# Level-1"], "metadata": {"id": "a7AbsH192Rom"}}, {"cell_type": "markdown", "metadata": {"id": "tZTn7S4f0CBW"}, "source": ["<div class=\"markdown-google-sans\">\n", "  <h1>Добро пожаловать в Colab!</h1>\n", "</div>\n", "\n", "<!-- TO<PERSON><PERSON>(b/319266067) remove temporary advert after a few weeks. -->\n", "<div class=\"markdown-google-sans\">\n", "  <h2>&#40;Новое&#41; Попробуйте Gemini API</h2>\n", "  <ul>\n", "  <li><a href=\"https://makersuite.google.com/app/apikey\">Generate a Gemini API key</a></li>\n", "  <li><a href=\"https://colab.research.google.com/github/googlecolab/colabtools/blob/main/notebooks/Talk_to_Gemini_with_Google%27s_Speech_to_Text_API.ipynb?utm_medium=link&utm_campaign=gemini\">Talk to Gemini with the Speech-to-Text API</a></li>\n", "  <li><a href=\"https://colab.research.google.com/github/googlecolab/colabtools/blob/main/notebooks/Learning_with_Gemini_and_ChatGPT.ipynb?utm_medium=link&utm_campaign=gemini\">Compare Gemini with ChatGPT</a></li>  \n", "  <li><a href=\"https://colab.google/notebooks/?utm_medium=link&utm_campaign=gemini\">More notebooks</a></li>\n", "  </ul>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "Nma_JWh-W-IF"}, "source": ["Уже знакомы с Colab? В этом видео рассказывается о функциях, которые вы могли пропустить: интерактивных таблицах, истории выполненного кода и палитре команд.\n", "\n", "<center>\n", "  <a href=\"https://www.youtube.com/watch?v=rNgswRZ2C1Y\" target=\"_blank\">\n", "  <img alt='Значок видео с изображением трех полезных функций Google Colab' src=\"data:image/png;base64,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\" height=\"188\" width=\"336\">\n", "  </a>\n", "</center>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zwFnJsE6vjf8"}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "5fCEDCU_qrC0"}, "source": ["<div class=\"markdown-google-sans\">\n", "  <h2>Что такое Colab?</h2>\n", "</div>\n", "\n", "Colaboratory, или просто <PERSON>, позволяет писать и выполнять код Python в браузере. При этом:\n", "- не требуется никакой настройки;\n", "- бесплатный доступ к графическим процессорам;\n", "- предоставлять доступ к документам другим людям очень просто.\n", "\n", "Это отличное решение для <strong>студентов</strong>, <strong>специалистов по обработке данных</strong> и <strong>исследователей в области искусственного интеллекта</strong>. Чтобы узнать больше, посмотрите <a href=\"https://www.youtube.com/watch?v=inN8seMm7UI\">ознакомительное видео</a> или начните работу с инструментом ниже."]}, {"cell_type": "markdown", "metadata": {"id": "GJBs_flRovLc"}, "source": ["<div class=\"markdown-google-sans\">\n", "\n", "## <strong>Начало работы</strong>\n", "</div>\n", "\n", "Документ, который вы читаете, размещен не на статической веб-странице, а в интерактивной среде под названием <strong>блокнот Colab</strong>, позволяющей писать и выполнять код.\n", "\n", "Например, вот <strong>ячейка</strong> с коротким скриптом Python, который позволяет рассчитать значение, выразить его в виде переменной и распечатать результат:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "id": "gJr_9dXGpJ05", "outputId": "9f556d03-ec67-4950-a485-cfdba9ddd14d"}, "outputs": [{"data": {"text/plain": ["86400"]}, "execution_count": 0, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["seconds_in_a_day = 24 * 60 * 60\n", "seconds_in_a_day"]}, {"cell_type": "markdown", "metadata": {"id": "2fhs6GZ4qFMx"}, "source": ["Чтобы выполнить код в ячейке выше, выберите ее, а затем нажмите кнопку воспроизведения слева от кода или используйте сочетание клавиш Cmd/Ctrl + Ввод. Чтобы изменить код, достаточно нажать на ячейку.\n", "\n", "Переменные, заданные в одной ячейке, можно будет использовать в других ячейках:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "id": "-gE-Ez1qtyIA", "outputId": "94cb2224-0edf-457b-90b5-0ac3488d8a97"}, "outputs": [{"data": {"text/plain": ["604800"]}, "execution_count": 0, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["seconds_in_a_week = 7 * seconds_in_a_day\n", "seconds_in_a_week"]}, {"cell_type": "markdown", "metadata": {"id": "lSrWNr3MuFUS"}, "source": ["Благодаря блокнотам Colab вы можете использовать в одном документе <strong>исполняемый код</strong>, <strong>форматированный текст</strong>, <strong>изображения</strong>, <strong>разметку HTML</strong>, <strong>набор LaTeX</strong> и не только. Блокноты Colab будут храниться на вашем Google Диске. Вы сможете открыть к ним доступ коллегам или друзьям, разрешив им просматривать или даже редактировать документ, а также оставлять комментарии. Подробная информация доступна на <a href=\"/notebooks/basic_features_overview.ipynb\">этой странице</a>. Чтобы создать блокнот Colab, можно воспользоваться меню \"Файл\" выше или <a href=\"http://colab.research.google.com#create=true\">перейти по этой ссылке</a>.\n", "\n", "Блокноты Colab – это блокноты Jupyter, которые размещены в сервисе Colab. Подробная информация о проекте Jupyter доступна на сайте <a href=\"https://www.jupyter.org\">jupyter.org</a>."]}, {"cell_type": "markdown", "metadata": {"id": "UdRyKR44dcNI"}, "source": ["<div class=\"markdown-google-sans\">\n", "\n", "## Ана<PERSON><PERSON>з и обработка данных\n", "</div>\n", "\n", "Colab позволяет использовать для анализа и визуализации данных все возможности популярных библиотек Python. Например, в ячейке ниже используется библиотека <strong>numpy</strong> для генерации случайных данных, а также библиотека <strong>matplotlib</strong> для их визуализации. Чтобы изменить код, достаточно нажать на ячейку."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "C4HZx7Gndbrh", "outputId": "46abc637-6abd-41b2-9bba-80a7ae992e06"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": []}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "\n", "ys = 200 + np.random.randn(100)\n", "x = [x for x in range(len(ys))]\n", "\n", "plt.plot(x, ys, '-')\n", "plt.fill_between(x, ys, 195, where=(ys > 195), facecolor='g', alpha=0.6)\n", "\n", "plt.title(\"Sample Visualization\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "4_kCnsPUqS6o"}, "source": ["Вы можете импортировать в блокноты Colab данные из своего аккаунта Google Диска, в том числе из таблиц, а также из GitHub и многих других источников. Чтобы узнать больше об импорте данных и о том, как можно использовать Colab для их анализа и обработки, изучите ссылки в разделе <a href=\"#working-with-data\">Работа с данными</a>."]}, {"cell_type": "markdown", "metadata": {"id": "OwuxHmxllTwN"}, "source": ["<div class=\"markdown-google-sans\">\n", "\n", "## Машинное обучение\n", "</div>\n", "\n", "В Colab вы можете импортировать набор данных изображения, сориентировать на него классификатор изображений и оценить модель с помощью <a href=\"https://colab.research.google.com/github/tensorflow/docs/blob/master/site/en/tutorials/quickstart/beginner.ipynb\">нескольких строк кода</a>. Код в блокнотах Colab исполняется на облачных серверах Google. Это означает, что вы можете использовать аппаратное обеспечение Google, <a href=\"#using-accelerated-hardware\">в том числе графические процессоры и TPU</a>, независимо от мощности вашей машины. Вам нужен только браузер."]}, {"cell_type": "markdown", "metadata": {"id": "ufxBm1yRnruN"}, "source": ["Colab активно используется в области машинного обучения, в том числе для:\n", "- знакомства с TensorFlow;\n", "- разработки и обучения нейронных сетей;\n", "- экспериментов с TPU;\n", "- распространения исследований в области ИИ;\n", "- создания руководств.\n", "\n", "Примеры использования блокнотов Colab в сфере машинного обучения приведены в разделе <a href=\"#machine-learning-examples\">Примеры использования в машинном обучении</a> ниже."]}, {"cell_type": "markdown", "metadata": {"id": "-Rh3-Vt9Nev9"}, "source": ["<div class=\"markdown-google-sans\">\n", "\n", "## Ресурсы по теме\n", "\n", "### Работа с блокнотами в Colab\n", "\n", "</div>\n", "\n", "- [Общие сведения о Colaboratory](/notebooks/basic_features_overview.ipynb)\n", "- [Руководство для Markdown](/notebooks/markdown_guide.ipynb)\n", "- [Импорт библиотек и установка зависимостей](/notebooks/snippets/importing_libraries.ipynb)\n", "- [Сохранение и загрузка блокнотов в GitHub](https://colab.research.google.com/github/googlecolab/colabtools/blob/main/notebooks/colab-github-demo.ipynb)\n", "- [Интерактивные формы](/notebooks/forms.ipynb)\n", "- [Интерактивные виджеты](/notebooks/widgets.ipynb)\n", "\n", "<div class=\"markdown-google-sans\">\n", "\n", "<a name=\"working-with-data\"></a>\n", "### Работа с данными\n", "</div>\n", "\n", "- [Загрузка данных: Д<PERSON><PERSON><PERSON>, Таблицы и Google Cloud Storage](/notebooks/io.ipynb)\n", "- [Диаграмма: визуализация данных](/notebooks/charts.ipynb)\n", "- [Начало работы с BigQuery](/notebooks/bigquery.ipynb)\n", "\n", "<div class=\"markdown-google-sans\">\n", "\n", "### Экспресс-курс по машинному обучению\n", "\n", "<div>\n", "\n", "Вот несколько блокнотов из онлайн-курса по машинному обучению от Google. Ещё больше информации доступно на <a href=\"https://developers.google.com/machine-learning/crash-course/\">сайте курса</a>.\n", "- [Знакомство с Pandas DataFrame](https://colab.research.google.com/github/google/eng-edu/blob/main/ml/cc/exercises/pandas_dataframe_ultraquick_tutorial.ipynb)\n", "- [Линейная регрессия в tf.keras с использованием синтетических данных](https://colab.research.google.com/github/google/eng-edu/blob/main/ml/cc/exercises/linear_regression_with_synthetic_data.ipynb)\n", "\n", "<div class=\"markdown-google-sans\">\n", "\n", "<a name=\"using-accelerated-hardware\"></a>\n", "### Использование ускорителей\n", "</div>\n", "\n", "- [Tensor<PERSON><PERSON> с графическими процессорами](/notebooks/gpu.ipynb)\n", "- [TensorFlow с TPU](/notebooks/tpu.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "P-H6Lw1vyNNd"}, "source": ["<div class=\"markdown-google-sans\">\n", "\n", "<a name=\"machine-learning-examples\"></a>\n", "\n", "### Примеры\n", "\n", "</div>\n", "\n", "- <a href=\"https://colab.research.google.com/github/NVIDIA/NeMo/blob/stable/tutorials/VoiceSwapSample.ipynb\">NeMo Voice Swap</a>. Инструменты разговорного AI Nvidia NeMo позволяют использовать в аудиозаписи сгенерированный компьютером голос вместо человеческого.\n", "\n", "- <a href=\"https://tensorflow.org/hub/tutorials/tf2_image_retraining\">Обучение классификатора изображений</a>. Используя предварительно обученный классификатор изображений, создайте модель Keras для распознавания цветов.\n", "- <a href=\"https://tensorflow.org/hub/tutorials/tf2_text_classification\">Классификация текста</a>. Разделите отзывы на сайте IMDb на <em>положительные</em> и <em>отрицательные</em>.\n", "- <a href=\"https://tensorflow.org/hub/tutorials/tf2_arbitrary_image_stylization\">Перенос стиля</a>. Используйте модель глубокого обучения, чтобы переносить стиль с одного изображения на другое.\n", "- <a href=\"https://tensorflow.org/hub/tutorials/retrieval_with_tf_hub_universal_encoder_qa\">Вопросно-ответный универсальный многоязычный кодировщик</a>. Используйте модель машинного обучения, чтобы отвечать на вопросы из набора данных SQuAD.\n", "- <a href=\"https://tensorflow.org/hub/tutorials/tweening_conv3d\">Интерполяция видео.</a> Спрогнозируйте, что произошло между первым и последним кадрами видео.\n"]}], "metadata": {"colab": {"provenance": [{"file_id": "1C_DIKdUE6u7R_OTBLV2-8CuNLcas7jqB", "timestamp": 1709797835511}, {"file_id": "/v2/external/notebooks/intro.ipynb", "timestamp": 1708595946264}], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}