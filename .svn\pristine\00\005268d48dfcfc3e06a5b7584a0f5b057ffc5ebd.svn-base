ODA({ is: 'oda-jupyter-tree', imports: '@oda/tree', extends: 'oda-tree',
    allowFocus: true,
    get dataSet() {
        return [this.notebook];
    },
    cellTemplate: 'oda-jupyter-tree-cell',
    onTapRows(e) {
        this.$super('oda-table', 'onTapRows', e);
        this.jupyter.scrollToCell?.(e.target.item, 0, 1, true, -28);
    },
    autoFixRows : true,
    hideRoot: true,
    hideTop: true,
 });
ODA({is: 'oda-jupyter-tree-cell', extends: 'oda-table-cell, this',
    template: /*html*/`
        <style>
            span{
                padding: 0px 8px;
            }
            oda-button:hover{
                border-radius: 50%;
                @apply --success-invert;
            }
        </style>
        <oda-button ~if="item.type ==='code'" :icon @down.stop="onTap" :error="!!item?.fn" :info-invert="item?.autoRun" :success="!item?.time" style="border-radius: 50%;"></oda-button>
    `,
    bold:{
        $attr: true,
        get(){
            return !!this.item?.h || this.item?.constructor.name === 'JupyterNotebook';
        }
    },
    error:{
        $attr: true,
        get(){
            let error = (this.item?.type === 'code' && this.item?.status === 'error');
            if (error)
                this.domHost.setAttribute('error', '');
            else
                this.domHost.removeAttribute('error');
            return error;
        }
    },
    dimmed:{
        $attr: true,
        get(){
            return this.item?.hideCode;
        }
    },
    get icon(){
        return  this.jupyter.getCell(this.item.id)?.fn? 'av:stop': 'av:play-circle-outline';
    },
    onTap(e) {
        this.jupyter.getCell(this.item.id)?.run();
    }
})