{"cells": [{"cell_type": "code", "breakpoints": "23", "outputs": [], "source": "ODA({is: 'oda-file-open',\n    template: `\n        <style>\n            :host{\n                @apply --vertical;\n            }\n        </style>\n        <h2>\n            Загрузка текстового файла\n        </h2>\n        <input accept=\".*\" type=\"file\" @change=\"readFile\">\n    `,\n    readFile(e){\n        let file = e.target.files[0];\n        if (!file) \n            return;\n        let reader = new FileReader();\n        reader.onload = function(e) {\n        };\n        reader.readAsText(file);\n    }\n})\nshow('oda-file-open');\ntree = ODA.createElement('oda-structure-tree', {\n    contextItem: this.contextItem, \n    allowFocus: true,\n    dblClickFocuseMode: false\n});\ntree.addEventListener('focused-item-changed', (e)=>{\n    if(e.detail.value){\n        ODA.push('выбран файл: '+host.textFile?.label)\n    }\n})\nshow(tree)", "metadata": {"id": "wDlWLbfkJ45622", "autoRun": true, "hideCode": true}}, {"cell_type": "code", "outputs": [], "source": "log(123)", "metadata": {"id": "wDlWLbfkJtvu222", "autoRun": true}}, {"cell_type": "text", "source": ["markdown..."], "metadata": {"id": "808rhytdbe2"}}, {"cell_type": "html", "source": ["<h1><span style=\"color: rgb(255, 0, 221); font-family: &quot;Comic Sans MS&quot;\">HTML editor</span></h1>"], "metadata": {"id": "808491dbe2", "previewMode": "iframe", "isEditMode": true, "editorHeight": "300px"}}, {"cell_type": "code", "outputs": [], "source": "\ntop.color = 'red'\nlog(`top.color = ${top.color}`)\nlog('x '.repeat(512), '\\n x '.repeat(512))\nfor (var i = 0; i < 100; i++) { log(Math.pow(2, i)) }\n", "metadata": {"id": "wDlWLbfkJtvu"}}, {"cell_type": "code", "outputs": [], "source": "import {NeuroModule, nm, BinLayer} from 'http://127.0.0.1:5500/web/oda/apps/neuronet/genius/neuro-module/neuro-module.js';\nimport {tensor} from 'http://127.0.0.1:5500/web/oda/apps/neuronet/genius/torus/torus.js';", "metadata": {"id": "efg<PERSON><PERSON><PERSON><PERSON>"}}, {"cell_type": "code", "breakpoints": "1 3 7", "outputs": [], "source": "    let _x = 50;\n    let _k = 30;\n    let _y = 100;\n    let _z = 200;\n    let _m = 70;\nx = tensor.param(tensor.arange([_x, _k, _m]))\nlog(x.toString())\ny = tensor.param(tensor.arange([_y, _x]))\nz = tensor.param(tensor.arange([_z, _y]))\n//log(y)\nres = tensor.einsum('xkm, yx, zy, xkm->ykz', [x, y, z, x])\nlog(res.toString())\n//res.back(res.data);\n//log(x.grad, y.grad\n", "metadata": {"id": "98908234"}}]}