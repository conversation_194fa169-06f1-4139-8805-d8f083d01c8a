import { getSearchParam } from "./util.js";

const TIMEOUT = 604800000; // millis in week
export const LOGIN_STORAGE_KEY = 'logins';
export const SECURITY_STORAGE_KEY = 'security';
const SECURITY_ERRORS_LOG_KEY = 'security_errors';

export function getFromLS(key) {
  const val = localStorage.getItem(key);
  return val && JSON.parse(val) || null;
}
/**
 * @param {string} key
 */
export function setToLS(key, val) {
  return localStorage.setItem(key, JSON.stringify(val));
}
/**@param {string} key */
export function removeFromLS(key) {
  return localStorage.removeItem(key);
}
/**
 * @param {{[key: string]: string} & {method?: string}} apiParams
 * @param {RequestInit} [init]
 */
export function API(apiParams, init) {
  try {
    const url = new URL(`${location.origin}/api`);
    for (const m in apiParams) {
      url.searchParams.set(m, apiParams[m]);
    }
    return fetch(url, init);
  }
  catch (err) {
    logError(err);
    throw err;
  }
}

function logError(error) {
  const log = getFromLS(SECURITY_ERRORS_LOG_KEY)?.slice(-500) || [];
  log.push({
    date: (new Date()).toISOString(),
    location: (window.top || window).location.toString(),
    type: error.constructor.name,
    message: error.message,
    stack: error.stack
  });
  setToLS(SECURITY_ERRORS_LOG_KEY, log);
}

export function storeLoginData(login, data = {}) {
  const logins = getFromLS(LOGIN_STORAGE_KEY) || {};
  for (const l in logins) {
    if (l !== login) {
      if (logins[l].date + TIMEOUT < Date.now()) {
        delete logins[l];
      } else {
        logins[l].current = false;
      }
    }
  }
  logins[login] = { date: Date.now(), current: true, ...data };
  setToLS(LOGIN_STORAGE_KEY, logins);
}
export function purgeLoginData(login) {
  const logins = getFromLS(LOGIN_STORAGE_KEY) || {};
  delete logins[login];
  setToLS(LOGIN_STORAGE_KEY, logins);
}

export function getLogins() {
  return getFromLS(LOGIN_STORAGE_KEY) || {};
}

export function setLogins(logins) {
  setToLS(LOGIN_STORAGE_KEY, logins);
}

/**
 * @returns {SecurityInfo}
 */
export function getSecurity() {
  return getFromLS(SECURITY_STORAGE_KEY) || {};
}
/**
 * @param {SecurityInfo} security
 */
export function setSecurity(security) {
  setToLS(SECURITY_STORAGE_KEY, security);
}

export function getCurrentLogin() {
  return getSecurity().login || '';
}

export function purgeLogin(login) {
  const logins = getFromLS(LOGIN_STORAGE_KEY) || {};
  delete logins[login];
  setToLS(LOGIN_STORAGE_KEY, logins);
}

export async function findAccount(login) {
  const response = await API({ method: 'find_account', login });
  const json = await response.json();
  return json.result;
}

export async function checkLogin(login) {
  const response = await API({ method: 'check_login', login });
  const json = await response.json();
  return json.result;
}

export async function goToRefreshAccessToken() {
  const loginOrigin = await findAccount();
  const url = new URL(`/web/auth/refresh/index.html`, loginOrigin);
  url.searchParams.set('redirect_url', location.toString());
  location.assign(url);
}

export function goToAuthorize(token, login, redirect_url = getSearchParam('redirect_url') || location.origin) {
  const url = new URL(`/web/auth/authorize.html`, (new URL(redirect_url)).origin);
  url.searchParams.set('token', token);
  url.searchParams.set('login', login);
  url.searchParams.set('redirect_url', redirect_url);
  setTimeout(() => {
    location.assign(url);
  }, 1000);
}

export async function saveAuthorize(
  login = getSearchParam('login'),
  token = getSearchParam('token') || getSearchParam('code')
) {
  const redirect_url = getSearchParam('redirect_url') || '/';
  const security = getSecurity();
  security.version = getTokenVersion(token);
  security.login = login;
  const res = await authorize(token);
  const uid = res?.result || res;
  if (res) {
    security.token = security.version < 3
      ? token
      : undefined;
    security.id = uid;
    setSecurity(security);
    location.assign(redirect_url);
  }
}
/**
 * @param {string} token - access-token or onetime-password (pin)
 */
export async function authorize(token) {
  try {
    const response = await API({ method: 'authorize' }, { method: 'POST', body: token });
    if (response.ok) {
      const json = await response.json();
      return json.result;
    }
    else {
      const text = await response.text();
      if (text === 'token expired\nstd::exception, execute') {
        goToRefreshAccessToken();
      }
      throw new Error(text);
    }
  }
  catch (err) {
    logError(err);
    throw err;
  }
}
export async function checkSsid() {
  try {
    const response = await API({ method: 'check_ssid' });
    if (response.ok) {
      const json = await response.json();
      return json.result;
    }
    else {
      throw new Error(await response.text());
    }
  }
  catch (err) {
    logError(err);
    throw err;
  }

}
export async function logout() {
  try {
    await API({ method: 'logout' });
    removeFromLS('security');
  }
  catch (err) {
    logError(err);
    throw err;
  }
}
export function getTokenVersion(token) {
  if (token.includes('.')) {
    return 3;
  }
  else {
    return 2;
  }
}


/**@param {ServiceInfo} info */
async function createService(info) {
  try {
    /**@type {typeof AuthService}*/
    const service = (await import(`./${info.type}/service.js`)).default;
    return new service(info);
  }
  catch (err) {
    console.warn(`Service ${JSON.stringify(info)} not allowed`, err);
    return new DisabledService(info);
  }
}

export async function getMethods() {
  /**@type {ServiceInfo[]} */
  const methodInfos = await (await API({ method: 'get_methods', version: '2' })).json();

  return (await Promise.all(methodInfos.map(createService))).filter(s => s && s.allowUse);
  // return [{ "name": "pap", "label": "Simple password", "type": "pap" }, { "name": "bis-search", "label": "BIS search", "type": "ldap" }, { "name": "bis-direct", "label": "BIS direct", "type": "ldap" }, { "name": "bis-ad2", "label": "BIS ActiveDirectory2", "type": "ldap" }, { "name": "bis-ad", "label": "BIS ActiveDirectory", "type": "ldap" }];
}

/**@implements {ServiceInfo}*/
export class AuthService {
  #info = null;
  disabled = false;
  constructor(info) {
    this.#info = info;
  }
  get name() { return this.#info.name; }
  get label() { return this.#info.label || this.name; }
  get type() { return this.#info.type; }

  get allowUse() {
    return true;
  }

  static authVersion = 2;

  /** @returns {Promise<*>} */
  static async authenticate() {
    throw new Error('AuthService is not implemented');
  }
}
class DisabledService extends AuthService {
  disabled = true;
}

/**
 * @typedef ServiceInfo
 * @prop {string} label
 * @prop {string} name
 * @prop {string} type
 *
*/

/**
 * @typedef SecurityInfo
 * @prop {string=} login
 * @prop {number=} version
 * @prop {string=} token - хранится только для версий < 3
 * @prop {string=} id
 */