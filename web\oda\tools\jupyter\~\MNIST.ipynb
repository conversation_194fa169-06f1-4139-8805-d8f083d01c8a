{"cells": [{"cell_type": "code", "metadata": {"id": "google-map", "hideCode": true, "hideOutput": false}, "source": ["// google-map-test\nshow('<iframe src=\"https://www.google.com/maps/embed?pb=!1m10!1m8!1m3!1d2793.816117697252!2d39.74576502281489!3d54.628038298661046!3m2!1i1024!2i768!4f13.1!5e1!3m2!1sru!2sru!4v1730885538275!5m2!1sru!2sru\" width=\"100%\" height=\"600\" style=\"margin: 0 auto; border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>')"], "outputs": [], "state": ""}, {"cell_type": "code", "metadata": {"id": "video", "hideCode": true, "hideOutput": false}, "source": ["// video-test\nshow('<iframe width=\"720\" height=\"405\" src=\"https://rutube.ru/play/embed/ad722c6958696f78f1908384f85120e8/\" style=\"box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.5); padding: 16px;margin: 16px auto;\" frameBorder=\"0\" allow=\"clipboard-write; autoplay\" webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>')\n"], "outputs": [], "state": ""}, {"cell_type": "text", "source": ["# Импорты"], "metadata": {"id": "ebc07ddc57", "collapsed": true}}, {"cell_type": "code", "metadata": {"id": "1215b33d681", "hideRun": false, "hideOutput": false}, "source": ["import {torus} from '/web/oda/apps/neuronet/genius/torus/torus.js';\nimport {nn} from '/web/oda/apps/neuronet/genius/neuro-module/neuro-module.js';\nimport {tensor} from '/web/oda/apps/neuronet/genius/torus/torus.js';\nimport '/web/oda/apps/neuronet/genius/neuro-module/loss-chart/loss-chart.js';"], "outputs": [], "state": ""}, {"cell_type": "text", "source": ["# Обновление параметров"], "metadata": {"id": "1022673708c", "collapsed": true}}, {"cell_type": "code", "source": ["torus.prototype.updateParams = function () {\r\n        if (!this.isParam) return;\r\n\r\n\r\n        let lr = torus.LEARNING_RATE\r\n        for(let i = 0; i<this.data.length; i++){\r\n            let change = this.grad[i] * lr //* torus.generator();\r\n            this.data[i] += change;\r\n        }\r\n\r\n\r\n            // for(let i = 0; i<this.data.length; i++){\r\n            //     let gamma = torus.generator();\r\n            //     let prev = this.prev[i] * gamma;\r\n            //     lr = 1 - gamma;\r\n            //     let change = prev + this.grad[i] * lr;\r\n            //     this.data[i] += change;\r\n            //     this.prev[i] = change\r\n            // }\r\n\r\n\r\n            // let lr = torus.LEARNING_RATE || .01\r\n            // for(let i = 0; i<this.data.length; i++){\r\n            //     let prev = this.prev[i];\r\n            //     let change = this.grad[i] * lr + prev * .5;\r\n            //     this.data[i] += change;\r\n            //     this.prev[i] = change;\r\n            // }\r\n\r\n\r\n            //\r\n\r\n            // for(let i = 0; i<this.data.length; i++){\r\n            //     const lr = torus.generator() / 3;\r\n            //     const lambda = 1 - lr;\r\n            //     let prev = this.prev[i];\r\n            //     let change = (this.grad[i] + lambda * prev) * lr;\r\n            //     this.data[i] += change;\r\n            //     this.prev[i] = change * lr;\r\n            //\r\n            // }\r\n\r\n        this.clearGrad();\r\n    }"], "metadata": {"id": "186b0f4da0", "hideOutput": false}, "outputs": [], "state": ""}, {"cell_type": "text", "source": ["# Класс Мнист"], "metadata": {"id": "13102fc4b42", "collapsed": false}}, {"cell_type": "code", "metadata": {"id": "110ebbffa46", "hideRun": false, "hideOutput": false}, "source": ["MNIST =  class MNIST extends nn.NeuroModule{\n    constructor(dim = 28) {\n        super(arguments);\n    }    \n    __init__(){\n        let netSape = [this.dim * this.dim, (this.dim * this.dim) / 4, 10]\n        this.layer1 = nn.Linear(netSape[0], netSape[1], true)\n        this.layer2 = nn.Linear(netSape[1], netSape[2], true)\n    }    \n    forward(x, target, back = true){\n        x = tensor.from(x)\n        x = this.layer1(x)\n        x = x.sigm()\n        x = this.layer2(x)\n        x = x.softmax();\n        if(target){\n            const loss = x.crossEntropy(target);\n            let loss_ = loss.get()\n            // if (!Number.isNaN(loss_)) {\n                if(!back)\n                    return loss\n\n                this.losses.push(loss_);\n                loss.back();\n            // }\n            // else console.log ('loss isNaN')\n        }\n        return x\n    }\n}"], "outputs": [], "state": ""}, {"cell_type": "text", "source": ["# Получение датасета"], "metadata": {"id": "190f2000b88"}}, {"cell_type": "code", "metadata": {"id": "5df214bef8", "hideRun": false, "hideOutput": false}, "source": ["datasetRaw = await ODA.loadJSON('/web/oda/apps/neuronet/mnist/mnist.json');\ntargets = targets = Array(10).fill(0).map((_,i)=> {\n    let t =  Array(10).fill(0)\n    t[i]=1\n    return t\n})"], "outputs": [], "state": ""}, {"cell_type": "code", "source": ["getTrain = (dataset = datasetRaw, batch=5, test_size=0.2) => {\n        let [result, res] = [{inputs:[],targets:[]},[]]\n\n        let suBatch = Array(10).fill(0).map((_,i)=>i) //.sort(()=>Math.random() - 0.5)\n        suBatch.forEach(n=>{\n            // let target  = Array(10).fill(0);\n            // target[n] = 1\n            let target  = targets[n]\n            for (let j=0; j<batch; j++) {\n                let br = Math.floor(dataset[n].length * (1-test_size))\n                let k = Math.floor(Math.random() * br)\n                res.push([dataset[n][k].flat(),target])\n            }\n        })\n\n        res.sort(()=>Math.random() - 0.5)\n        res.forEach ( ([inp,tar]) => {result.inputs.push(inp); result.targets.push(tar)} )\n        return result\n    }"], "metadata": {"id": "721a4f9d6b", "hideOutput": false}, "outputs": []}, {"cell_type": "text", "source": ["# Элементы отображения"], "metadata": {"id": "9649e50be4", "collapsed": false}}, {"cell_type": "code", "metadata": {"id": "1123b3d3bb", "hideRun": false, "hideOutput": false, "hideCode": true}, "source": ["ODA({is: 'oda-mnist-input', imports: '@oda/button', template: /*html*/ `\n        <style> :host { @apply --vertical; } </style>\n        <svg style='margin:1em;' width='281' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 281 281' preserveAspectRatio=\"xMidYMin meet\">\n            <rect x=\"0\" y=\"0\" width=\"281\" height=\"281\" fill='#4d85cf'/>\n            <rect ~for='rects' :x=\"$for.item?.x\" :y=\"$for.item?.y\" width=\"9\" height=\"9\" :fill='$for.item?.v?\"#070637\":\"#a9caff\"'\n                    @mousemove=\"_mouseMove($event, $for.item)\"/>\n        </svg>\n        <div horizontal style='margin:-1em 1em 1em 1em; width:281px; background: #a9caff;'>\n            <oda-button :disabled icon-size=\"18\" :icon=\"drawBoll?'bootstrap:vector-pen':'bootstrap:eraser-fill'\" @tap='drawBoll=!drawBoll'></oda-button>\n            <i flex></i>\n            <oda-button :disabled icon-size=\"18\" icon=\"bootstrap:trash3-fill\" @tap='input= new Array(28*28).fill(0)'></oda-button>\n        </div> `,\n    get rects() { return this.input.map((v,i)=> Object( {v, x:(i%28)*10+1, y:(~~(i/28))*10+1}) ) },\n    input: { $def: new Array(28*28).fill(0) },\n    drawBoll:true, disabled:false,\n    _mouseMove(x,e) {\n        if (!x.buttons) return\n        if (e.v===this.drawBoll) return\n        this.input[Math.floor(e.y/10)*28 + Math.floor(e.x/10)] = this.drawBoll\n    },\n});\n\nODA({is: 'oda-mnist-output', imports: '@oda/button', template: /*html*/ `\n    <svg style='margin:1em;' width=\"281\" height=\"281\" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 281 281'>\n        <rect x=\"0\" y=\"0\" width=\"281\" height=\"281\" fill='#a9caff'/>\n        <g ~for='result' :transform=\"'translate(0,'+ ($for.index * 28) +')'\">\n            <rect x=\"2\" y=\"2\" :width=\"276*$for.item\" height=\"24\" :fill='maxIndex===$for.index? \"#ff00ff55\" : \"#4d85cf55\"'/>\n            <text x=\"265\" y=\"16\" font-size='16' dominant-baseline=\"middle\" fill=\"#070637\">{{$for.index}}</text>\n            <text x=\"5\" y=\"16\" font-size='16' dominant-baseline=\"middle\" fill=\"#070637\">{{($for.item*100).toFixed(2)}} %</text>\n        </g>\n        <text x=\"150\" y=\"160\" font-size='200' dominant-baseline=\"middle\" text-anchor=\"middle\" fill=\"#070637\">{{maxIndex}}</text>\n    </svg> `,\n    result:[.1, .01, .1, .01, .28, .1, .1, .1, .1, .1],\n    get maxIndex() { return this.result.reduce((a,b,i,c)=>c[a]>b?a:i,0) },\n});\n\nODA({is: 'oda-mnist-visual', template: /*html*/ `\n    <div horizontal>\n        <oda-mnist-input ::input :disabled ></oda-mnist-input>\n        <oda-mnist-output :result='output' ></oda-mnist-output>\n        <oda-loss-chart style='margin:1em;' flex :data='loss'></oda-loss-chart>\n    </div>`,\n    input: { $def: new Array(28*28).fill(0) }, disabled:false,\n    output: [.05, .01, .15, .01, .28, .1, .1, .1, .1, .1],\n    loss:[]\n});  "], "outputs": [], "state": ""}, {"cell_type": "code", "source": ["visual = this.createElement('oda-mnist-visual', {})\n// show(visual)\n"], "metadata": {"id": "1482082ffa5", "hideOutput": false}, "outputs": [], "state": ""}, {"cell_type": "text", "source": "", "metadata": {"id": "c9e7d91322"}}, {"cell_type": "code", "source": ["mnist = new MNIST()\n\n\ntrane = () => {\n        let rez, dat = getTrain(undefined, 1);\n        for (let i=0; i<dat.inputs.length; i++) {\n            rez = mnist(dat.inputs[i], dat.targets[i])\n            if (i==(dat.inputs.length-1)) {\n                visual.input = dat.inputs[i].map(a=>a)\n                visual.output = Array.from(rez.data)\n                \n        //         // this.lastLoss = mnist.loss.toFixed(4)\n                visual.loss = mnist.losses\n            }\n        }\n}\n// trane()\n"], "metadata": {"id": "a62c893c6a", "hideOutput": false}, "state": "", "outputs": []}, {"cell_type": "code", "source": ["ODA({is: 'oda-mnist-play', imports: '@oda/button', template: /*html*/ `\n    <oda-button :label='go?\"pause\":\"play\"' :icon='go?\"av:pause\":\"av:play-arrow\"' @tap=\"_go()\"></oda-button>`,\n    go:false,\n    _go(){ this.go=!this.go; this.steps(); },\n    steps(){ if (this.go) this.async(() => {trane(); this.steps();}, 100) },\n})\nbtnPlay = this.createElement('oda-mnist-play', {})\n"], "metadata": {"id": "bb0871f1a3"}, "outputs": []}, {"cell_type": "code", "source": ["// show(visual)\nshow(\"<iframe src='https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d476.2836683410046!2d39.74605682577753!3d54.627878585674765!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sru!2sru!4v1730879578803!5m2!1sru!2sru' width='800' height='600' style='margin: 0 auto; border:0;' allowfullscreen='' loading='lazy' referrerpolicy='no-referrer-when-downgrade'></iframe>\")\nshow(\"<h1 style='color: red;'>ЭТО ЗАГОЛОВОК H1</h1>\", \"<button>ЖМИ МЕНЯ</button>\")\nshow('<h2>ЭТО ПОДЗАГОЛОВОК H2<h2>',btnPlay,'<h3>ЭТО ПОДЗАГОЛОВОК H3<h3>')\nshow(visual)"], "metadata": {"id": "418ac57ccd"}, "outputs": [{"data": {"text/plain": "<h2>!!<h2>"}}]}, {"cell_type": "code", "metadata": {"id": "1098074c2e5", "hideRun": false, "autoRun": true, "hideOutput": false, "hideCode": false}, "source": ["ODA({is: 'oda-mnist', imports: '@oda/button', template: /*html*/ `\n    <style>\n        :host {max-width:686px; margin:auto; display:grid; grid-template-columns:1fr 1fr; grid-template-areas: 'input output' 'loss loss'; }\n        .block {border: 1px solid #4d85cf; padding:10px; margin:10px;}\n        .box { margin:10px}\n        .block h3 {margin:0 10px; display:flex; color:#070637; align-items: center;}\n        .block h3 i {font-size:0.8em; opacity:0.8;}\n        oda-button {border: 1px solid #4d85cf; margin:10px;}\n        h3  oda-button {margin:1px;padding:1px;}\n        .block h3 input {border:0; width:1em; outline:none; font-style:italic; font-weight:bold; font-size:15px}\n    </style>\n    <div class='block' style='grid-area:input;'>\n        <h3>input:<i flex></i>\n            <oda-button :disabled='go' icon-size=\"18\" icon=\"bootstrap:trash3-fill\" @tap='input=new Array(28*28).fill(0)'></oda-button>\n            <oda-button :disabled='go' icon-size=\"18\" :icon=\"drawBoll?'bootstrap:vector-pen':'bootstrap:eraser-fill'\" @tap='drawBoll=!drawBoll'></oda-button>\n        </h3>\n        <svg class='box' width=\"281\" height=\"281\" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 281 281'>\n            <rect x=\"0\" y=\"0\" width=\"281\" height=\"281\" fill='#4d85cf'/>\n            <rect ~for='rects' :x=\"$for.item?.x\" :y=\"$for.item?.y\" width=\"9\" height=\"9\" :fill='$for.item?.v?\"#070637\":\"#a9caff\"'\n                    @mousemove=\"_mouseMove($event, $for.item)\"/>\n        </svg>\n    </div>\n    <div class='block' style='grid-area:output;'>\n        <h3><b>output:</b><i flex></i><i>target:<input @input='_inTarget' :value='target'/></i>\n            <oda-button :disabled='go' icon-size=\"18\" icon=\"carbon:ibm-watson-openscale\" @tap='_dopTrain()'></oda-button>\n        </h3>\n        <svg class='box' width=\"281\" height=\"281\" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 281 281'>\n            <rect x=\"0\" y=\"0\" width=\"281\" height=\"281\" fill='#a9caff'/>\n            <g ~for='result' :transform=\"'translate(0,'+ ($for.index * 28) +')'\">\n                <rect x=\"2\" y=\"2\" :width=\"276*$for.item\" height=\"24\" :fill='maxIndex===$for.index? \"#ff00ff55\" : \"#4d85cf55\"'/>\n                <text x=\"265\" y=\"16\" font-size='16' dominant-baseline=\"middle\" fill=\"#070637\">{{$for.index}}</text>\n                <text x=\"5\" y=\"16\" font-size='16' dominant-baseline=\"middle\" fill=\"#070637\">{{($for.item*100).toFixed(2)}} %</text>\n            </g>\n            <text x=\"150\" y=\"160\" font-size='200' dominant-baseline=\"middle\" text-anchor=\"middle\" fill=\"#070637\">{{maxIndex}}</text>\n        </svg>\n    </div>\n    <div class='block' style='grid-area:loss;'>\n        <h3><b>loss: {{lastLoss}} </b><i flex></i><i>generation: {{generation}}</i></h3>\n        <svg class='box' :width :height xmlns='http://www.w3.org/2000/svg' :viewBox=\"'0 0 ' + width + ' ' + height\">\n            <rect x=\"0\" y=\"0\" :width :height fill='#a9caff'/>\n            <path :d='pathD' fill=\"none\" stroke=\"#070637\" stroke-width=\"2\"/>\n        </svg>\n    </div>\n    <oda-button :label='go?\"pause\":\"play\"' :icon='go?\"av:pause\":\"av:play-arrow\"' @tap=\"_go()\"></oda-button>\n    `,\n    _inTarget(e) {\n        if (!e.data) return\n        let old = this.target\n        if ((47<e.data.charCodeAt())&(e.data.charCodeAt()<58)) e.target.value =  this.target = e.data\n        else  e.target.value =  this.target = old\n    },\n    _dopTrain(){\n        this.mnist.generation+=1\n        let rez = this.mnist(this.input, targets[this.target])\n        this.result = rez.data\n    },\n    iconFromChar(c) { /// 🗑️✍🏻✒️♻💊✍🏿\n        return /*html*/ `data:image/svg+xml,\n            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'>\n                <text y='.9em' x='-8' font-size='90'>${c}</text>\n            </svg>`},\n    get rects() {\n        return this.input.map((v,i)=> {\n            let [x,y] = [i%28, ~~(i/28)].map(a=>a*10+1)\n            return {x,y,v}\n        } )\n    },\n    get maxIndex() { return this.result.reduce((a,b,i,c)=>c[a]>b?a:i,0) },\n\n    get pathD(){\n        if (this.pointsL.length<2) return ''\n        let padding = Math.min(this.width,this.height)/10\n        let [w,h] = [this.width,this.height].map(x=>x-padding*2)\n        let dx = w/(this.pointsL.length-1)\n        let [min,max] = [Math.min(...this.pointsL),Math.max(...this.pointsL)]\n        let points = this.pointsL.map((v,i)=>[i*dx+padding,h-(v-min)/(max-min)*h+padding])\n        let rez = 'M ' + points[0].join(' ') + ' '\n        if (this.bezier) rez+=  'Q ' + points[0].join(' ') + ', ' + points[1].join(' ') + ' '\n        else  rez += 'L ' + points[1].join(' ') + ' '\n        points.slice(2).forEach(e => rez += (this.bezier?'T ':'L ') + e.join(' ') + ' ' )\n        return rez\n    },\n\n    get pointsL() {\n        let [lL,lP] = [this.loss.length,this.maxPoint]\n        if (lL<lP) return this.loss\n        let newP = Array(lP-1).fill(0).map((_,i)=>{\n            let idx = Math.floor(i*(lL/(lP-1)))\n            let dL = Math.floor(lL/(lP-1))+1\n            return this.loss.slice(idx,idx+dL).reduce((a,c)=>a+c)/dL\n        })\n        newP.push(this.loss[lL-1])\n        return newP\n    },\n    bezier:false,\n\n    go:false,\n    _go(){\n        this.go = !this.go\n        this.steps()\n    },\n\n    width:281*2+10*6+2,\n    height:281,\n    maxPoint:100,\n\n    input: {\n        $def: new Array(28*28).fill(0)\n    },\n    result:[],\n    loss:{\n        $def:[]//new Array(20000).fill(0).map(_=> Math.random()*5-2)\n    },\n    drawBoll:true,\n    _mouseMove(x,e) {\n        if (!x.buttons) return\n        if (e.v===this.drawBoll) return\n\n        let idx = Math.floor(e.y/10)*28 + Math.floor(e.x/10)\n        this.input[idx] = this.drawBoll\n        this.result = Array.from(this.mnist(this.input).data)\n\n        // console.log(e.y,e.x ,e.y*28+e.x ,this.input[e.y*28+e.x])\n    },\n\n    mnist: new MNIST(),\n    getTrain:getTrain,\n\n    step() {\n        let rez, mnist = this.mnist, dat = this.getTrain(undefined, 1);\n        for (let i=0; i<dat.inputs.length; i++) {\n            rez = mnist(dat.inputs[i], dat.targets[i])\n            if (i==(dat.inputs.length-1)) {\n                this.input = dat.inputs[i].map(a=>a)\n                this.result = Array.from(rez.data)\n                // this.lastLoss = mnist.loss.toFixed(4)\n                this.generation = mnist.losses.length\n            }\n        }\n        this.loss= mnist.losses\n\n    },\n    steps(){\n        if (!this.go) return;\n        this.async(() => {\n            this.step()\n            this.steps()\n        }, 100)\n    },\n    lastLoss:'',\n    generation:0,\n    target:'',\n})\n\n\n"], "outputs": [], "state": ""}, {"cell_type": "html", "metadata": {"id": "8949b863a5", "isEditMode": false, "showPreview": true, "previewMode": "html", "editType": "code"}, "source": ["\n<oda-mnist></oda-mnist> \n      "]}, {"cell_type": "text", "source": ["# тест"], "metadata": {"id": "f87f76eb8f", "collapsed": false}}, {"cell_type": "code", "metadata": {"id": "5a7d878b1b", "hideRun": false, "hideOutput": false}, "source": ["// тест binarize\r\nfunction binarize (grad_i) {\r\n    let p = Math.max(0,Math.min(1,(grad_i+1)/2)) // σ(x) = hard sigmoid\r\n    return p>Math.random() // +1 with probability p = σ(x), -1 otherwise.\r\n}\r\n\r\nlet grad = -0.7\r\nprint (`σ(x) = ${Math.max(0,Math.min(1,(grad+1)/2))} = ${Math.max(0,Math.min(1,(grad+1)/2))*100} % ` )\r\n\r\nlet n = 1000\r\nlet rezz = Array(n).fill(grad).map(g => binarize(g)).reduce((akk,b) => { b? akk.true++:akk.false++; return akk },{true:0,false:0} )\r\n\r\nprint(`true: ${rezz.true}, (${rezz.true/n*100} %) `)"], "outputs": [{"data": {"text/plain": "σ(x) = 0.15000000000000002 = 15.000000000000002 % "}}, {"data": {"text/plain": "true: 135, (13.5 %) "}}], "state": ""}, {"cell_type": "code", "metadata": {"id": "14213e4b005", "hideRun": false, "hideOutput": false}, "source": ["// тест сложения\r\nv = Array(15).fill(0).map(a =>  (Math.random()>0.5)?0:1  ) // [0,1,1,1,0]\r\nmul = v.filter(b=>b).reduce((akk,b) => akk*(-1), 1 )>0\r\nprint (mul)\r\nresult = 'return (' + v.map((_,i)=>{\r\n                    return '(v['+i+'] - 1)';\r\n            }).filter(l=>l).join(` + `) + ')&1;\\n';\r\nprint(result)\r\nconst fn = new Function('v',result);\r\nprint(fn(v))\r\n// print(`in= ${v.join('')}  mul =`fn(v))"], "outputs": [{"data": {"text/plain": "false"}}, {"data": {"text/plain": "return ((v[0] - 1) + (v[1] - 1) + (v[2] - 1) + (v[3] - 1) + (v[4] - 1) + (v[5] - 1) + (v[6] - 1) + (v[7] - 1) + (v[8] - 1) + (v[9] - 1) + (v[10] - 1) + (v[11] - 1) + (v[12] - 1) + (v[13] - 1) + (v[14] - 1))&1;\n"}}, {"data": {"text/plain": "0"}}], "state": ""}], "metadata": {}, "nbformat": 4, "nbformat_minor": 2}