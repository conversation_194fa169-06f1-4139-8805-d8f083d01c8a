:host {
    font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: var(--content-color);
    background: var(--content-background);
}
.md-wasm p {
    margin: 4px;
}
h1, h2, h3, h4, h5, h6 {
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    font-weight: 500;
}
h1 {
    margin-bottom: 8px;
    font-size: 2.0736em;
}
h2 {
    font-size: 1.728em;
    margin-bottom: 6px;
}
h3 {
    font-size: 1.44em;
    margin-bottom: 4px;
}
h4 {
    font-size: 1.2em;
    margin-bottom: 3px;
}
h5 {
    font-size: 1.14em;
    margin-bottom: 2px;
}
h6 {
    font-size: 0.83333em;
    margin-bottom: 2px;
}
hr {
    opacity: .2;
}
.hint {
    text-align: right;
    color: #555555;
    font-size: small;
}
.hint a {
    color: #555555;
}
textarea {
    height: 0;
}
.preview {
    padding: 4px;
    width: 100%;
    height: 100%;
}
blockquote {
    color: #6a737d;
    margin: 1em 2em;
    padding: 0 1em;
    border-left: 5px solid lightgray;
}
pre {
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 2px;
    margin: 1em 0;
    padding: 12px 8px;
    font-family: monospace;
    font-size: 14px;
    color: #333;
    word-break: break-all;
    word-wrap: break-word;
}
code {
    font-size: 90%;
}
:not(pre)>code {
    background-color: #f7f7f7;
    border-radius: 2px;
    padding: 0 3px
}
tbody tr:nth-child(odd) {
    background: #ececec;
}
tbody tr:hover {
    background: #d6d6d6;
}
table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
}
th {
    font-weight: bold;
    background: var(--header-background);
    color: var(--header-color);
    fill: var(--header-color);
    border: 1px solid var(--border-color);
    padding: 2px 6px;
}
td {
    border: 1px solid var(--border-color);
    padding: 2px 6px;
}
img {
    max-width: 90%;
    height: auto;
}
